<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <process-btn
      ref="processBtn"
      :gps="gps"
      :processBtn="processBtn"
      :formData="appFormValue"
      :dialogClose="dialogClose"
      :on-ok="handleDoFun"
    ></process-btn>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="font-weight: 700">在办信访/线索查询</div>
      <sb-el-form
        ref="appForm"
        :form="appForm"
        v-model="appFormValue"
        @uploadFileList="uploadFileList"
        :disabled="appForm.formDisabled"
        :on-ok="handleDoFun"
      >
      </sb-el-form>
    </div>
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
import ProcessBtn from "@/components/Process/ProcessBtn";
import { getDictList } from "@/api/public";
import { deleteDraft } from "@/api/process";
import {
  getProcessInfo,
  getFormDetail,
  saveDraft,
  startProcess,
} from "@/api/apply/application";
import { uploadProcessFiles } from "@/api/public";


let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  // applyUser: store.getters.user.truename,
  // applyUserName: store.getters.user.username,
  // belongCompanyName: store.getters.user.belongCompanyName,
  // belongDepartmentName: store.getters.user.belongDepartmentName,
  // applyTime: util.getNow(),
  blank: "blank",
};

export default {
  name: "application",
  components: { ProcessBtn },
  props: {
    href: {
      type: Object,
      default() {
        return {};
      },
    },
    // 关闭
    dialogClose: {
      type: Function,
    },
  },
  data() {
    return {
      gps: this.href,
      processDefKey: "wgrcsw_process",
      processBtn: {
        optClose: false,
      },
      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd"),

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "150px",
        inline: true,
        formItemList: [
          
          {
            class: "c12",
            label: "工单标题",
            key: "title",
            type: "input",
            show:false
          },
           {
            class: "c4",
            label: "发起单位",
            key: "belongCompanyName",
            type: "input",
            disabled: true,
            rule: { required: true },
          },
          {
            class: "c4",
            label: "发起部门",
            key: "belongDepartmentName",
            type: "input",
            disabled: true,
            rule: { required: true },
          },
          {
            class: "c4",
            label: "发起人",
            key: "applyUser",
            type: "input",
            disabled: true,
            rule: { required: true },
          },
          {
            class: "c4",
            label: "发起人OA",
            key: "applyUserName",
            type: "input",
            disabled: true,
            show: false,
          },
          {
            class: "c4",
            label: "发起人电话",
            key: "applyPhone",
            type: "input",
            disabled: true,
            rule: { required: true },
          },
         
          {
            class: "c4",
            label: "申请时间",
            key: "applyTime",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            disabled: true,
            rule: { required: true },
          },
          {
            class: "c4",
            label: "申请权限类型",
            key: "taskTypeValue",
            type: "select",
            dictType: "taskType",
            rule: { required: true },
          },
          {
            class: "c12",
            label: "申请事由",
            key: "inferiorFactionIllustration",
            type: "input",
            inputType: "textarea",
            rule: { required: true,  },
          },
           {
            class: "c12",
            label: "附件",
            key: "formFiles",
            type: "sbUpload",
            btnText: "+",
            fun: "uploadFileList",
            listType: "text",
            multiple: true,
            rule: { required: true,  },
          },
        ],
      },
    };
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    // console.log("gps", JSON.parse(JSON.stringify(this.gps)));

    this.initValue = {
      applyUser: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyPhone: this.$store.getters.user.preferredMobile,
      applyTime: this.nowTime,
    };
    this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    // 初始化
    initFun() {
      this.gps.processDefKey = this.processDefKey;

      // 起草及草稿不显示“工单编号”
      // if(!this.gps.location || this.gps.location=="wgrcsw.start"){
      // 	var index = this.appForm.formItemList.findIndex(item => item.key==="workNumber");
      // 	if(index > -1){
      // 		this.appForm.formItemList[index].show = false;
      // 	}
      // 	this.appFormValue = JSON.parse(JSON.stringify(this.appFormValue));
      // }
      // 加载表单
      if (this.gps.location || (this.gps.action && this.gps.action == "read")) {
        this.loadForm();
      }
    },

    // 获取工单详情
    loadForm() {
      var data = {
        pmInsId: this.gps.pmInsId,
        processDefKey: this.gps.processDefKey,
      };
      getFormDetail(data).then((res) => {
        this.appFormValue = res.data;

        this.appFormValue.blank = "blank";

        // 设置只读
        if (
          (this.gps.type != "draft" && this.gps.type != "task") ||
          this.gps.location != "lzdapt.start"
        ) {
          this.appForm.formDisabled = true;
          this.appForm.formItemList(el=>{
            if(el.type == 'sbUpload'){
                el.disabled = true;
            }
          })
        } else {
          this.appForm.formDisabled = false;
        }
      });
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData)
        .then((res) => {
          obj.content.onSuccess(res, obj.content.file, []);
        })
        .catch((error) => {
          obj.content.onError();
        });
    },

    // 重置表单
    handleFormReset() {
      this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);
    },

    beforeSubmit() {},

    // 流转下一步
    handleNextBtn() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          this.$refs["processBtn"].doProcessNext();
        }
      });
    },

    // 保存草稿
    handleSaveDraft() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          if (this.clickFlag) {
            this.clickFlag = false;

            saveDraft({
              processDefKey: this.processDefKey,
              title: this.appFormValue.title,
              formData: this.appFormValue,
            })
              .then((res) => {
                this.clickFlag = true;
                if (!this.gps.location) {
                  this.$router.push({ name: "processDraft" });
                } else {
                  this.dialogClose();
                }
              })
              .catch((err) => {
                this.clickFlag = true;
              });
          }
        }
      });
    },

    // 废除草稿
    handleAbolish() {
      if (this.clickFlag) {
        this.clickFlag = false;
        deleteDraft({
          pmInsId: this.gps.pmInsId,
          processDefKey: this.processDefKey,
        })
          .then((res) => {
            this.clickFlag = false;
            this.dialogClose();
          })
          .catch((err) => {
            this.clickFlag = true;
          });
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  },
};
</script>
<style>
.w99 {
  width: 99%;
  margin: 0 auto;
}

.p10 {
  padding: 15px;
}

.tip {
  font-size: 13px;
  color: #a5a5a5;
  margin: 20px 0 10px;
}

.frequencyBox {
  display: flex;
  justify-content: space-around;
}

.frequencyBox p {
  width: 15%;
  line-height: 32px;
  text-align: center;
}

.frequencyBox > .el-input {
  width: 24%;
}

.frequencyBox > .el-input .el-input__inner {
  padding: 0 4px;
  text-align: center;
  border: 1px solid #dcdfe6;
}

.frequencyBox > .el-select {
  width: 29%;
}

.frequencyBox > .el-select .el-input__inner {
  padding: 0 4px 0 6px;
  border: 1px solid #dcdfe6;
}
</style>
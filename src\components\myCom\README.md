# 用户管理组件说明文档

## 概述
本模块包含用户信息管理的完整功能，由多个组件协同工作，实现用户信息的录入、编辑和保存。

## 组件结构

### 1. addUser.vue (主组件)
- **功能**: 用户添加/编辑的主页面
- **职责**: 协调各个子组件，收集和保存所有数据
- **数据收集**: 在`handleSave`方法中收集所有子组件的表格数据

### 2. familyTable.vue (家庭关系组件)
- **功能**: 管理家庭成员及重要社会关系信息
- **数据字段**: `usFamilySocialRelationList`

### 3. clueTable.vue (问题线索组件)
- **功能**: 管理问题线索及信访情况
- **包含表格**:
  - `nowCuleList`: 在办线索
  - `doCuleList`: 已办线索
  - `nowLettersList`: 在办信访
  - `doLettersList`: 已办信访

### 4. handleTable.vue (处理情况组件)
- **功能**: 管理各类处理情况
- **包含表格**:
  - `nowCuleList`: 党纪政务处分情况
  - `doCuleList`: 组织处理情况
  - `nowLettersList`: 其他处理情况

### 5. extendTable.vue (扩展信息组件)
- **功能**: 管理领导干部的扩展信息
- **包含16个表格**:
  - `giftRefusalList`: 领导干部拒收或上交礼金礼品情况
  - `profitActivityList`: 领导干部从事或参与营利性活动或兼职持股信息
  - `housingList`: 领导干部本人、配偶及共同生活子女住房情况
  - `marriageChangeList`: 领导干部婚姻变化情况
  - `celebrationList`: 领导干部参与操办本人及近亲属婚丧喜庆事宜情况
  - `abroadList`: 领导干部本人配偶、子女及其配偶出国（境）情况
  - `investigationList`: 领导干部本人配偶、子女及其配偶受到查处或涉嫌犯罪情况
  - `employmentList`: 领导干部配偶、子女及其配偶从业情况
  - `majorReportList`: 领导干部其他重大事项报告
  - `auditList`: 领导干部经济责任审计情况
  - `handlingList`: 因不如实报告个人有关事项受到处理的情况
  - `dutyReportList`: 班子成员述职述廉材料
  - `inspectionReportList`: 巡察报告中对领导班子成员的基本评价部分
  - `democraticLifeList`: 民主生活会对照检查材料
  - `activityInfoList`: 活情况信息
  - `otherIntegrityList`: 其他反应廉政情况的材料
  - `formFileValue`: 附件信息

### 6. incorruptTable.vue (廉政情况组件)
- **功能**: 管理廉政回复情况
- **包含表格**:
  - `nowList`: 廉政回复情况

## 数据流程

### 保存流程 (handleSave方法)
1. **表单验证**: 验证基本信息表单
2. **数据收集**:
   - 合并奖惩信息到主表单
   - 收集家庭关系数据 → `usFamilySocialRelationList`
   - 收集问题线索数据 → `usProblemClueSituationList`
   - 收集处理情况数据 → `usDisciplinaryInfoList`
   - 收集扩展信息数据 → 16个us开头的标准字段
   - 收集廉政情况数据 → `usHonestReplyInfoList`
3. **数据保存**: 调用API保存完整数据

### 字段映射关系
| 组件数据源 | 标准字段名 | 说明 |
|-----------|-----------|------|
| familyTable.usFamilySocialRelationList | usFamilySocialRelationList | 家庭社会关系列表 |
| clueTable.* | usProblemClueSituationList | 问题线索情况列表（合并4个表格） |
| handleTable.* | usDisciplinaryInfoList | 纪律处分信息列表（合并3个表格） |
| extendTable.giftRefusalList | usLeaderGiftRejectList | 领导干部拒收或上交礼金礼品情况列表 |
| extendTable.profitActivityList | usLeaderProfitActivityList | 领导干部从事或参与营利性活动或兼职持股信息列表 |
| extendTable.housingList | usLeaderHousingList | 领导干部本人、配偶及共同生活子女住房情况列表 |
| extendTable.marriageChangeList | usLeaderMarriageChangeList | 领导干部婚姻变化情况列表 |
| extendTable.celebrationList | usLeaderCelebrationList | 领导干部参与操办本人及近亲属婚丧喜庆事宜情况列表 |
| extendTable.abroadList | usLeaderFamilyAbroadInfoList | 领导家属国外情况列表 |
| extendTable.investigationList | usLeaderFamilyCrimeInfoList | 领导家属违法犯罪情况列表 |
| extendTable.employmentList | usLeaderFamilyEmploymentList | 领导家属就业情况列表 |
| extendTable.majorReportList | usLeaderOtherMattersList | 领导干部其他重大事项报告列表 |
| extendTable.auditList | usLeaderEcoAuditList | 领导经济责任审计列表 |
| extendTable.handlingList | usFalseReportHandlingList | 虚假举报处理列表 |
| extendTable.dutyReportList | usTeamMemberReportList | 班子成员报告列表 |
| extendTable.inspectionReportList | usLeadershipEvaluationList | 领导班子评价列表 |
| extendTable.democraticLifeList | usDemocraticLifeCheckList | 民主生活会检查列表 |
| extendTable.activityInfoList | usActivityInfoList | 活动信息列表 |
| extendTable.otherIntegrityList | usOtherIntegrityMaterialList | 其他廉政材料列表 |
| incorruptTable.nowList | usHonestReplyInfoList | 廉政回复信息列表 |

### 数据结构
```javascript
appFormUserValue = {
  // 基本信息字段...
  // 奖惩信息字段...

  // 标准字段名映射
  usFamilySocialRelationList: [],           // 家庭社会关系列表
  usProblemClueSituationList: [],           // 问题线索情况列表
  usDisciplinaryInfoList: [],               // 纪律处分信息列表
  usLeaderGiftRejectList: [],               // 领导干部拒收或上交礼金礼品情况列表
  usLeaderProfitActivityList: [],           // 领导干部从事或参与营利性活动或兼职持股信息列表
  usLeaderHousingList: [],                  // 领导干部本人、配偶及共同生活子女住房情况列表
  usLeaderMarriageChangeList: [],           // 领导干部婚姻变化情况列表
  usLeaderCelebrationList: [],              // 领导干部参与操办本人及近亲属婚丧喜庆事宜情况列表
  usLeaderFamilyAbroadInfoList: [],         // 领导家属国外情况列表
  usLeaderFamilyCrimeInfoList: [],          // 领导家属违法犯罪情况列表
  usLeaderFamilyEmploymentList: [],         // 领导家属就业情况列表
  usLeaderOtherMattersList: [],             // 领导干部其他重大事项报告列表
  usLeaderEcoAuditList: [],                 // 领导经济责任审计列表
  usFalseReportHandlingList: [],            // 虚假举报处理列表
  usTeamMemberReportList: [],               // 班子成员报告列表
  usLeadershipEvaluationList: [],           // 领导班子评价列表
  usDemocraticLifeCheckList: [],            // 民主生活会检查列表
  usActivityInfoList: [],                   // 活动信息列表
  usOtherIntegrityMaterialList: [],         // 其他廉政材料列表
  usHonestReplyInfoList: []                 // 廉政回复信息列表
}
```

## 使用说明

### 添加新记录
1. 填写基本信息表单
2. 填写奖惩信息
3. 在各个表格中添加相关记录
4. 点击保存按钮

### 编辑现有记录
1. 系统自动加载现有数据
2. 修改需要更新的信息
3. 点击保存按钮

### 表格操作
- **添加记录**: 点击各表格的"添加记录"按钮
- **删除记录**: 点击表格行的"删除"按钮
- **编辑记录**: 直接在表格单元格中编辑

## 注意事项
1. 所有表格数据都会在保存时自动收集
2. 表单验证只针对基本信息，表格数据无强制验证
3. 保存时会在控制台输出调试信息，便于开发调试
4. 各组件通过ref引用进行数据访问

## 开发维护
- 如需添加新的表格字段，需要同时更新对应组件的数据结构和handleSave方法
- 所有表格操作都遵循统一的添加/删除模式
- 组件间通过props传递配置信息，通过ref访问数据

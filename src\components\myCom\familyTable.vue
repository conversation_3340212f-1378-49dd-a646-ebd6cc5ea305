<template>
  <div>
    <div class="message tableForm" style="margin-top: 0px">
      <div class="orderTitle" style="font-weight: 700">
        家庭成员及重要社会关系
      </div>
      <div class="flex j-s a-c m-title2">
        <span></span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd()" size="small"
            >添加家庭成员</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="usFamilySocialRelationList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column
        type="index"
        label="序号"
        width="50">
        </el-table-column>
        <el-table-column label="称谓" align="center">
          <template v-slot:default="scope">
           <el-select v-model="scope.row.appellation" size="mini" placeholder="请选择">
                <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
             <el-input
             size="mini"
              v-model="scope.row.personName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="出生年月" align="center">
          <template v-slot:default="scope">
            <el-date-picker
            v-model="scope.row.birthDate"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            size="mini"
            placeholder="请选择出生年月"
          >
          </el-date-picker>
          </template>
        </el-table-column>
         <el-table-column label="政治面貌" align="center">
          <template v-slot:default="scope">
           <el-select v-model="scope.row.politicalStatus" size="mini" placeholder="请选择">
                <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="工作单位及职务" align="center">
          <template v-slot:default="scope">
            <el-input
            size="mini"
              v-model="scope.row.workUnitAndPosition"
              placeholder="请输入工作单位及职务"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="身份证号" align="center">
          <template v-slot:default="scope">
            <el-input
            size="mini"
              v-model="scope.row.idCard"
              placeholder="请输入身份证号"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="手机号" align="center">
          <template v-slot:default="scope">
            <el-input
            size="mini"
              v-model="scope.row.mobilePhone"
              placeholder="请输入手机号"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelete(scope.$index)"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
     gps: {
      type: Object,
      default() {
        return this.$route.query;
      },
     }
  },
  data() {
    return {
        usFamilySocialRelationList:[],
        options: [
            {
                value: '1',
                label: '1',
            },
            {
                value: '2',
                label: '2',
            },
            {
                value: '3',
                label: '3',
            },
        ]
    }
  },
  methods: {
    handleAdd() {
      this.usFamilySocialRelationList.push({
        appellation: '',
        personName: '',
        birthDate: '',
        politicalStatus: '',
        workUnitAndPosition: '',
        idCard: '',
        mobilePhone: '',

      })
    },
    handledelete(index){
        this.usFamilySocialRelationList.splice(index, 1)
    }
  }
}

</script>
<style scoped>
.orderTitle{
    text-align: left;
      padding-left: 20px;
}
.m-title2 {
  width: 100%;
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
  padding: 6px 0;
}
::v-deep .el-button--text{
    color: rgba(192, 0, 0, 1);
}
::v-deep .tableCustom .el-input__prefix {
    top: 3px !important;
}
 ::v-deep .tableCustom .el-table__cell{
    padding: 4px 0 !important;
 }
.mr5 {
  margin-right: 5px;
}
::v-deep .el-table__empty-block {
  min-height: 40px;
}
::v-deep .el-table__empty-text {
  line-height: 40px;
}
</style>

<template>
  <div>
    <div class="pageInfo">
      <span v-show="showBtn.nextBtn" class="btn nextBtn" @click="handleNextBtn()">
        <svg-icon icon-class="random"></svg-icon>
        <font>流转下一步</font>
      </span>
      <!--			<span v-show="showBtn.cancel" class="btn cancel a_danger"><svg-icon icon-class="shanchu"></svg-icon><font>注销</font></span>-->
      <span v-show="showBtn.saveDraft" class="btn saveDraft" @click="handleSaveDraft()">
        <svg-icon icon-class="baocun"></svg-icon>
        <font>保存草稿</font>
      </span>
      <span v-show="showBtn.flowTrack" class="btn flowTrack" @click="handleFlowTrack()">
        <svg-icon icon-class="liuchengtu"></svg-icon>
        <font>流程跟踪</font>
      </span>
      <span v-show="showBtn.abolish" class="btn abolish" @click="handleAbolish()">
        <svg-icon icon-class="shanchu"></svg-icon>
        <font>废除草稿</font>
      </span>
      <!-- <span v-show="showBtn.viewComments" class="btn viewComments" @click="handleViewComments()">
        <svg-icon icon-class="chakan2"></svg-icon>
        <font>查看意见</font>
      </span> -->
      <span v-show="showBtn.processImg" class="btn processImg" @click="handleProcessImg()">
        <svg-icon icon-class="liuchengtu"></svg-icon>
        <font>流程图</font>
      </span>
      <!--			<span v-show="showBtn.printOut" class="btn printOut"><svg-icon icon-class="printing"></svg-icon><font>打印</font></span>-->
      <span v-show="showBtn.formReset" class="btn formReset" @click="handleFormReset()">
        <svg-icon icon-class="zhongzhi"></svg-icon>
        <font>重置</font>
      </span>
      <span class="btn optClose" @click="handleOptClose()">
        <svg-icon icon-class="close"></svg-icon>
        <font>关闭</font>
      </span>

      <span v-if="getProcessBtn()" class="btn" @click="handleDeleteProcess()"><svg-icon icon-class="shanchu"></svg-icon>
        <font>废除归档</font>
      </span>
      <span v-if="getProcessStopBtn()" class="btn" @click="handleStopProcess()"><svg-icon icon-class="close"></svg-icon>
        <font>终止</font>
      </span>

      <span class="btn" style="padding-top: 7px;" v-for="(item,index) in btnsArr" @click="allBtns(item.btnData)">{{item.btnName}}</span>
    </div>

    <!-- 流转下一步选人 -->
    <el-dialog title="流转下一步" :visible.sync="processD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1000px" class="nextFooter">
      <process-next ref="processNext" :key="pnKey" :gps="gps" :appFormValue="formData" :decisionTypeArr="decisionTypeArr"></process-next>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirm()" size="small">确认</el-button>
        <el-button @click="processD=false" size="small">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 流程跟踪 -->
    <el-dialog title="流程跟踪" :visible.sync="trackD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1200px">
      <process-track :key="trackKey" :gps="gps"></process-track>
    </el-dialog>

    <!-- 流程图 -->
    <el-dialog title="流程图" :visible.sync="diagramD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1000px">
      <process-diagram :key="diagramKey" :gps="gps"></process-diagram>
    </el-dialog>

    <!-- 查看意见 -->
    <el-dialog title="查看意见" :visible.sync="opinionD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1000px">
      <process-opinion :key="opinionKey" :gps="gps"></process-opinion>
    </el-dialog>
  </div>
</template>
<script>
import { startProcess } from '@/api/apply/application'
import { mapGetters } from "vuex";
import ProcessNext from "@/components/Process/ProcessNext";
import ProcessTrack from "@/components/Process/ProcessTrack";
import ProcessDiagram from "@/components/Process/ProcessDiagram";
import ProcessOpinion from "@/components/Process/ProcessOpinion";
export default {
  name: "ProcessBtn",
  components: { ProcessNext, ProcessTrack, ProcessDiagram, ProcessOpinion },
  props: {
    gps: {
      type: Object
    },
    processBtn: {
      type: Object
    },
    formData: {
      type: Object
    },
    dialogClose: {
      type: Function
    },
    onOk: {
      type: Function
    },
    decisionTypeArr: {
      type: Array
    },
    processBtnArr: {
      type: Array
    },
    formBtnsArr: {
      type: Array
    },
  },
  computed: {
    ...mapGetters(["tabnav"]),
    // 流程相关按钮显隐
    showBtn() {
      let btnObj = {
        nextBtn: !this.gps.location || this.gps.type == "draft" || (!this.gps.modify && (this.gps.type == "task" || this.gps.type == "toRead")),
        cancel: !this.gps.modify && this.gps.type == "task" && this.gps.location == this.formLocation,
        saveDraft: !this.gps.location || (this.gps.location && this.gps.type == "draft"),
        flowTrack: !this.gps.modify && (this.gps.type == "task" || this.gps.type == "join" || this.gps.type == "toRead" || this.gps.type == "doRead"),
        abolish: this.gps.location && this.gps.type == "draft",
        viewComments: !this.gps.modify && (this.gps.type == "task" || this.gps.type == "join" || this.gps.type == "toRead" || this.gps.type == "doRead"),
        processImg: !this.gps.modify && (this.gps.type == "task" || this.gps.type == "join" || this.gps.type == "toRead" || this.gps.type == "doRead"),
        printOut: this.archiveLocation && this.archiveLocation.indexOf(this.gps.location) > -1,
        formReset: !this.gps.location || this.gps.type == "draft",
        optClose: !this.gps.location
      };
      if (this.processBtn) {
        for (let i in this.processBtn) {
          btnObj[i] = this.processBtn[i];
        }
      }
      return btnObj;
    }
  },
  data() {
    return {
      btnsArr: [],
      formLocation: process.env.VUE_APP_APPCODE + ".start", //指定流程的起草环节名称（起草/退回修改）
      archiveLocation: process.env.VUE_APP_APPCODE + ".confirm", //指定流程中需要显示打印按钮的环节名称，默认为确认归档
      noNextUserDecisionId: "end",

      // 流程下一步
      pnKey: 0,
      processD: false,

      // 流程跟踪
      trackKey: 0,
      trackD: false,
      // 流程图
      diagramKey: 0,
      diagramD: false,
      // 查看意见
      opinionKey: 0,
      opinionD: false
    };
  },
  created() {
    this.getFormBtns()
  },
  mounted() { },
  methods: {
    getFormBtns() {
      let location = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + ".start"
      let btnsArr = []
      if(this.formBtnsArr && this.formBtnsArr.length > 0){
        this.formBtnsArr.forEach(element => {
          if (element[location]) {
            btnsArr.push(element[location + 'BtnInfo'])
          }
        });
        this.btnsArr = btnsArr
      }
    },
    // 流转下一步（表单校验等）
    handleNextBtn() {
      if (this.onOk) {
        this.onOk(null, "handleNextBtn");
      } else {
        this.$emit("handleNextBtn");
      }
    },
    // 流转下一步
    doProcessNext() {
      this.pnKey++;
      this.processD = true;
    },

    // 确认
    handleConfirm() {
      // 判断流程下一步页面数据是否加载完
      var isLoad = this.$refs["processNext"].isLoad;
      if (!isLoad) {
        return false;
      }

      var decisionUser = this.$refs["processNext"].decisionUser;
      var decisionData = this.$refs["processNext"].decisionData;
      var choosedUser = this.$refs["processNext"].choosedUser;
      var flag = true;
      var gName = "";
      for (var i in decisionUser) {
        for (var j in decisionUser[i]) {
          if ((decisionUser[i][j].requSel === true || decisionUser[i][j].requSel === "true") && choosedUser[i][j].length == 0) {
            flag = false;
            if (i == "copy" && decisionUser[i][j].group != "normalGrouping") {
              gName = decisionData[i].decisionName.split("#")[1] + decisionUser[i][j].group;
            }
            break;
          }
        }
      }

      if (!(choosedUser["main"].length>0) || (!flag && decisionData["main"].decisionId.toLowerCase() != "end" && decisionData["main"].decisionId.substr(decisionData["main"].decisionId.length - 4,decisionData["main"].decisionId.length ) != "_end")) {
        this.$message({
          message: "请选择" + gName + "审批人",
          type: "warning",
          duration: 1500
        });
        return false;
      }

      var decisionExpand = this.$refs["processNext"].decisionExpand();  //决策项过滤扩展规则
      if(!decisionExpand){
        return false;
      }

      var nextUser = [],
        nextUserName = [],
        nextUserOrgCode = [],
        nextUserPostId = [];
      for (var i in choosedUser["main"]) {
        for (var j in choosedUser["main"][i]) {
          nextUser.push(choosedUser["main"][i][j].id);
          nextUserName.push(choosedUser["main"][i][j].name);
          nextUserOrgCode.push(choosedUser["main"][i][j].parentId);
          nextUserPostId.push("");
        }
      }

      var data = {
        appCode: process.env.VUE_APP_APPCODE,
        type: !this.gps.location || (this.gps.type == "draft" && this.gps.location == process.env.VUE_APP_APPCODE + ".start") ? "START" : "FLOW",
        title: this.formData.title || "",
        processDefKey: this.gps.processDefKey || "",
        pmInsType: this.gps.pmInsType || "",
        // processDefId: this.gps.processDefKey || "",
        outcome: decisionData["main"].decisionId,
        taskDefinitionKey: decisionData["main"].targetActivityDefId,
        message: this.$refs["processNext"].opinion,
        nextUser: nextUser.join(","),
        nextUserName: nextUserName.join(","),
        nextUserOrgCode: nextUserOrgCode.join(","),
        nextUserPostId: nextUserPostId.join(","),
        nextActivityParam: decisionData['main'].nextActivityParam,
        formData: this.formData,
        receipTitle: this.formData.receipTitle || "",
      };
      if(this.formData.orderNo){
        data.orderNo = this.formData.orderNo
      }
      if(this.formData.serialNumber){
        data.serialNumber = this.formData.serialNumber
      }
      if (this.gps.tmpId) {
        data.tmpId = this.gps.tmpId
        data.pmInsId = this.formData.pmInsId
      }
      data.activityDefId = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + ".start";
      if (this.gps.taskId) data.taskId = this.gps.taskId;
      if (this.gps.processInstId) data.processInstId = this.gps.processInstId;
      // if(this.gps.taskDefinitionKey) data.taskDefinitionKey = this.gps.taskDefinitionKey;
      if (this.gps.processDefinitionId)
        data.processDefinitionId = this.gps.processDefinitionId;

      if (this.gps.type == 'toRead') {
        data.type = 'READ'
        data.notificationId = this.gps.notificationId
        data.copyMessage = this.$refs['processNext'].opinion
      }
      if (decisionData['main'].copyType && decisionData['main'].copyType == 'normal') {
        choosedUser['copy'] =  choosedUser['main']
        decisionData['copy'] =  decisionData['main']
        data.outcome = ''
        data.taskDefinitionKey = ''
        data.message = ''
        data.nextUser = ''
        data.nextUserName = ''
        data.nextUserOrgCode = ''
        data.nextUserPostId = ''
      }
      if (choosedUser['copy'].length > 0) {
        var nextcopyUser = []
        for (var i in choosedUser['copy']) {
          for (var j in choosedUser['copy'][i]) {
            nextcopyUser.push(choosedUser['copy'][i][j].id + '-' + choosedUser['copy'][i][j].name)
          }
        }
        data.copyNextUserNames = nextcopyUser.join(',')
        data.copyLocation = decisionData['copy'].decisionId
        data.copyLocationName = decisionData['copy'].targetActivityDefId.split(',')[1]
        data.copyMessage = decisionData['main'].copyType && decisionData['main'].copyType == 'normal' ? this.$refs['processNext'].opinion : this.$refs['processNext'].copyMessage
      }

      // this.processD = false;
      startProcess(data).then(res => {
        if (res.errcode === 0 || res.errcode === 200) {
          this.processD = false;
          if (this.gps.myFrom  && this.$router.currentRoute.path=="/workOrder") {
            //单点
            window.opener = null;
            window.open("", "_self");
            window.close();
          } else {
            if (!this.gps.location) {
              this.$router.push({ name: "processTask" });
            } else {
              this.dialogClose();
            }
          }
        } else{
          if (res.message != undefined && res.message != null) {
          }
        }
      });
    },

    // 重置
    handleFormReset() {
      if (this.onOk) {
        this.onOk(null, "handleFormReset");
      } else {
        this.$emit("handleFormReset");
      }
    },

    // 保存草稿
    handleSaveDraft() {
      if (this.onOk) {
        this.onOk(null, "handleSaveDraft");
      } else {
        this.$emit("handleSaveDraft");
      }
    },

    // 废除草稿
    handleAbolish() {
      if (this.onOk) {
        this.onOk(null, "handleAbolish");
      } else {
        this.$emit("handleAbolish");
      }
    },

    // 流程跟踪
    handleFlowTrack() {
      this.trackKey++;
      this.trackD = true;
    },

    // 流程图
    handleProcessImg() {
      this.diagramKey++;
      this.diagramD = true;
    },

    // 查看意见
    handleViewComments() {
      this.opinionKey++;
      this.opinionD = true;
    },

    // 关闭
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if(this.gps.myFrom && this.$router.currentRoute.path=="/workOrder"){//单点            
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        let item = this.tabnav.find(item => item.path === this.$route.path);
        this.$store.dispatch("CloseTabnav", item).then(res => {
          if (item.path === this.$route.path) {
            const lastTag = res.slice(-1)[0];
            // 前一个 tab-view 页面存在，就跳；不存在就到首页
            if (lastTag) {
              this.$router.push({ path: lastTag.path });
            } else {
              this.$router.push({ path: "/mywork/processTask" });
            }
          }
        });
      }
    },

    // 废除归档显隐
    getProcessBtn() {
      if (this.gps.type == 'task') {
        let arrs = this.processBtnArr
        if (!arrs) {
          return false
        } else {
          if (this.gps.location == process.env.VUE_APP_APPCODE + '.') {
            return false
          } else {
            let location = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + '.start'
            const apiInfo = arrs.filter((item) => item.activityDefId == location)
            return apiInfo[0].deletes
          }
        }
      }
      
    },
    // 终止显隐
    getProcessStopBtn() {
      if (this.gps.type == 'task') {
        let arrs = this.processBtnArr
        if (!arrs) {
          return false
        } else {
          if (this.gps.location == process.env.VUE_APP_APPCODE + '.') {
            return false
          } else {
            let location = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + '.start'
            const apiInfo = arrs.filter((item) => item.activityDefId == location)
            return apiInfo[0].stops
          }
        }
      }
    },

    // 废除归档
    handleDeleteProcess() {
      if (this.onOk) {
        this.onOk(null, "handleDeleteProcess");
      } else {
        this.$emit("handleDeleteProcess");
      }
    },
    // 终止
    handleStopProcess() {
      if (this.onOk) {
        this.onOk(null, "handleStopProcess");
      } else {
        this.$emit("handleStopProcess");
      }
    },

    // 动态按钮事件
    allBtns(item) {
      if(item.opernUrl){
        this.$router.push({ path: item.opernUrl });
      }
    },
  }
};
</script>
<style scope>
.w99 {
  width: 99%;
  margin: 0 auto;
}
.p10 {
  padding: 10px;
}
.el-dialog__body {
  padding: 20px 20px 0;
}
.nextFooter .el-dialog__footer {
  text-align: center;
}
</style>

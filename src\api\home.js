import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";
// 保存
export function disciplineAdd(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/creatUserBase`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}
// 编辑
export function disciplineEdit(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/updateUserBase`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}
// 详情
export function disciplineDetail(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/queryUserBase?userBaseId=${params.id}`,
        contentType: "application/json;charset=UTF-8",
        // data: params
    });
}
// 列表
export function getUserBaseList(params){
    let data = params?params:{};
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/getUserBaseList?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}
// 删除
export function disciplineDel(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/deleteUserBase?id=${params.id}`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 根据OA账户人所有信息
export function findUserInfo(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/findUserInfo`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}
// 查询组织信息
export function findOrgInfo(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/findOrgInfo`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}
// 模板下载
export function downloadUserFile() {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usUserBaseInfo/downloadTemplate`,
        contentType: 'application/json;charset=UTF-8',
        responseType: 'blob'
    });
}


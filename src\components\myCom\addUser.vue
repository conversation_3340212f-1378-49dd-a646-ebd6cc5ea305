<template>
  <div style="padding-bottom: 30px">
    <div class="pageInfo">
      <span class="btn saveDraft" @click="handleSave()">
        <svg-icon icon-class="baocun"></svg-icon>
        <font>保存</font>
      </span>
      <!-- <span class="btn formReset" @click="handleFormReset()">
        <svg-icon icon-class="zhongzhi"></svg-icon>
        <font>重置</font>
      </span> -->
      <span class="btn optClose" @click="handleOptClose()">
        <svg-icon icon-class="close"></svg-icon>
        <font>关闭</font>
      </span>
    </div>
    <div class="message tableForm">
      <sb-el-form
        ref="appFormUser"
        :form="appFormUser"
        v-model="appFormUserValue"
        :disabled="appFormUser.formDisabled"
        :on-ok="handleDoFun2"
      >
      </sb-el-form>
    </div>
    <div class="flex j-s a-c m-title">
      <span>奖惩信息</span>
    </div>
    <div class="message tableForm" style="margin-top: 0px">
      <sb-el-form
        ref="appFormRewards"
        :form="appFormRewards"
        v-model="appFormRewardsValue"
        :disabled="appFormRewards.formDisabled"
        :on-ok="handleDoFun2"
      >
      </sb-el-form>
    </div>

    <!-- 家庭情况 -->
    <familyTable ref="familyTable" :gps="gps" :list="familyList"></familyTable>

    <!-- 问题线索 -->
    <clueTable :gps="gps" ref="clueTable" :list="clueList"></clueTable>
    <!-- 处理 -->
    <handleTable :gps="gps" ref="handleTable" :list="clueList"></handleTable>
    <!-- 扩展 -->
    <extendTable :gps="gps" ref="extendTable" :list="clueList"></extendTable>
    <!-- 廉政 -->
    <incorruptTable
      :gps="gps"
      ref="incorruptTable"
      :list="clueList"
    ></incorruptTable>
  </div>
</template>

<script>
import { disciplineAdd, disciplineEdit, disciplineDetail } from "@/api/home";
import familyTable from "@/components/myCom/familyTable.vue";
import clueTable from "@/components/myCom/clueTable.vue";
import handleTable from "@/components/myCom/handleTable.vue";
import extendTable from "@/components/myCom/extendTable.vue";
import incorruptTable from "@/components/myCom/incorruptTable.vue";

export default {
  name: "addAdmin",
  components: {
    familyTable,
    clueTable,
    handleTable,
    extendTable,
    incorruptTable,
  },
  props: {
    gps: {
      type: Object,
      default() {
        return this.$route.query;
      },
    },
    types: { type: String },
    rowData: { type: Object },
  },
  computed: {},
  data() {
    return {
      familyList: [],
      clueList: [],
      initValue: {},
      clickFlag: true, //防止多次点击
      initValue: {},
      appFormRewardsValue: {},
      appFormRewards: {
        formDisabled: false,
        labelWidth: "150px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "奖惩情况",
            key: "rewardsAndPunishments",
            type: "input",
            inputType: "textarea",
            rule: { required: false },
          },
          {
            class: "c12",
            label: "任免理由",
            key: "appointmentReason",
            type: "input",
            inputType: "textarea",
            rule: { required: false },
          },
          {
            class: "c12",
            label: "年度考核结果",
            key: "annualEvaluationResult",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c12",
            label: "呈报单位",
            key: "reportingUnit",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c6",
            label: "填报时间",
            key: "reportingTime",
            type: "date",
            subtype: "datetime",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            rule: { required: false },
          },
          {
            class: "c6",
            label: "填表人",
            key: "reporter",
            type: "input",
            rule: { required: false },
          },
        ],
      },
      appFormUserValue: {},
      appFormUser: {
        formDisabled: false,
        labelWidth: "150px",
        inline: true,
        formItemList: [
          {
            class: "c3",
            label: "员工编号",
            key: "employeeId",
            type: "input",
            rule: { required: true },
          },
           {
            class: "c3",
            label: "姓名",
            key: "trueName",
            type: "input",
            placeholder: "",
          },
          // {
          //   class: "c3",
          //   label: "姓名",
          //   key: "trueName",
          //   type: "user",
          //   readonly: true,
          //   mulitple: false,
          //   stepLoad: true,
          //   appendShow: true,
          //   rule: { required: true },
          //   relevancy: "trueName-name,userName-id",
          //   defaultProps: {
          //     children: "children",
          //     label: "name",
          //     isLeaf: "leaf",
          //   },
          //   handleUser: "chooseFun",
          // },
          {
            class: "c3",
            label: "OA账号",
            key: "userName",
            type: "input",
            placeholder: "",
            show: false,
          },
          {
            class: "c3",
            label: "性别",
            key: "gender",
            type: "select",
            options: [
              {
                label: "男",
                value: "男",
              },
              {
                label: "女",
                value: "女",
              },
            ],
            rule: { required: true },
          },
          {
            class: "c3",
            label: "出生年月",
            key: "birthDate",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            rule: { required: true },
          },
          {
            class: "c3",
            label: "身份证号",
            key: "idCard",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c3",
            label: "年龄",
            key: "age",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c3",
            label: "民族",
            key: "ethnicity",
            type: "select",
            dictType: "taskType",
            rule: { required: true },
          },
          {
            class: "c3",
            label: "籍贯",
            key: "nativePlace",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c3",
            label: "出生地",
            key: "birthPlace",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "入党时间",
            key: "partyJoinDate",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "参加工作时间",
            key: "workStartDate",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "健康状况",
            key: "healthStatus",
            type: "select",
            dictType: "taskType",
            rule: { required: true },
          },
          {
            class: "c3",
            label: "专业技术职务",
            key: "professionalTitle",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c9",
            label: " ",
            key: "_blank",
            type: "input",
            placeholder: " ",
            disabled: true,
            show: true,
          },
          {
            class: "c12",
            label: "熟悉专业有何特长",
            key: "specialty",
            type: "input",
            inputType: "textarea",
            rule: { required: false },
          },
          {
            class: "c12",
            label: "照片",
            key: "photo",
            type: "sbUpload",
            btnText: "+",
            fun: "uploadFileList",
            listType: "text",
            multiple: false,
            rule: { required: true },
          },
          {
            class: "c3",
            label: "全日制教育学历",
            key: "fullTimeEducation",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "全日制教育毕业院校",
            key: "fullTimeSchool",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "全日制教育学位",
            key: "fullTimeDegree",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "全日制教育毕业院校专业",
            key: "fullTimeMajor",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "在职教育学历",
            key: "inServiceEducation",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "在职教育毕业院校",
            key: "inServiceSchool",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "在职教育学位",
            key: "inServiceDegree",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "在职教育毕业院校专业",
            key: "inServiceMajor",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "现任职务",
            key: "currentPosition",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c3",
            label: "拟任职务",
            key: "proposedPosition",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "拟免职务",
            key: "toBeRemovedPosition",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "工作单位名称",
            key: "workUnitName",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "工作单位地址",
            key: "workUnitAddress",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "职级",
            key: "rank",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "现任职务时间",
            key: "currentPositionStartDate",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "任职状态",
            key: "employmentStatus",
            type: "input",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "办公电话",
            key: "officePhone",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "移动电话",
            key: "mobilePhone",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "人大代表",
            key: "npcRepresentative",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "政协委员",
            key: "cppccMember",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "党委委员",
            key: "partyMember",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "纪委委员",
            key: "disciplineMember",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "监委委员",
            key: "supervisoryCommissionMember",
            type: "select",
            dictType: "xueli",
            rule: { required: false },
          },
          {
            class: "c3",
            label: "人员部门",
            key: "department",
            type: "input",
            rule: { required: false },
          },
          {
            class: "c12",
            label: "个人简历",
            key: "personalResume",
            type: "input",
            inputType: "textarea",
            rule: { required: false },
          },
        ],
      },
    };
  },
  created() {},
  mounted() {
    this.fetchDetail();
  },

  methods: {
    fetchDetail() {
      if (this.types == "edit") {
        disciplineDetail({
          id: this.rowData.id,
        }).then((res) => {
          this.appFormUserValue = res.data;
          this.dataListXueli = res.data.educationalBackgrounds;
          this.dataListWork = res.data.workExperiences;
          this.appFormUserValue.professionalCategory =
            res.data.professionalCategory?.split(",");
        });
      }
    },

    chooseFun(obj, data) {
      let myorgDisplayName = data[0].orgDisplayName.split("\\");
      this.appFormUserValue.company = myorgDisplayName[0];
    },
    // 重置表单
    handleFormReset() {
      this.appFormUserValue = Object.assign({}, this.initValue);
    },
    // 验证学历信息
    validateEducationalBackgrounds(data) {
      const validItems = [];
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        // 检查是否为空项（所有字段都为空）
        const isEmpty =
          !item.startTime && !item.endTime && !item.schoolName && !item.major;

        if (!isEmpty) {
          // 有数据的项需要验证必填字段
          if (!item.startTime) {
            this.$message({
              message: `学历信息第${i + 1}行：开始时间不能为空`,
              type: "warning",
              duration: 2000,
            });
            return false;
          }
          if (!item.schoolName) {
            this.$message({
              message: `学历信息第${i + 1}行：学校名称不能为空`,
              type: "warning",
              duration: 2000,
            });
            return false;
          }
          if (!item.major) {
            this.$message({
              message: `学历信息第${i + 1}行：专业名称不能为空`,
              type: "warning",
              duration: 2000,
            });
            return false;
          }
          validItems.push(item);
        }
      }
      return validItems;
    },

    // 验证工作经历
    validateWorkExperiences(data) {
      const validItems = [];
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        // 检查是否为空项（所有字段都为空）
        const isEmpty =
          !item.startTime && !item.endTime && !item.unitName && !item.position;

        if (!isEmpty) {
          // 有数据的项需要验证必填字段
          if (!item.startTime) {
            this.$message({
              message: `工作经历第${i + 1}行：开始时间不能为空`,
              type: "warning",
              duration: 2000,
            });
            return false;
          }
          if (!item.unitName) {
            this.$message({
              message: `工作经历第${i + 1}行：单位名称不能为空`,
              type: "warning",
              duration: 2000,
            });
            return false;
          }
          if (!item.position) {
            this.$message({
              message: `工作经历第${i + 1}行：职位名称不能为空`,
              type: "warning",
              duration: 2000,
            });
            return false;
          }
          validItems.push(item);
        }
      }
      return validItems;
    },

    // 保存草稿
    handleSave() {
      this.$refs["appFormUser"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
        
          // 验证学历信息
          const validEducationalBackgrounds =
            this.validateEducationalBackgrounds(this.dataListXueli);
          if (validEducationalBackgrounds === false) {
            return false;
          }

          // 验证工作经历
          const validWorkExperiences = this.validateWorkExperiences(
            this.dataListWork
          );
          if (validWorkExperiences === false) {
            return false;
          }

          if (this.clickFlag) {
            this.clickFlag = false;
            // 使用过滤后的有效数据
            this.appFormUserValue.educationalBackgrounds =
              validEducationalBackgrounds;
            this.appFormUserValue.workExperiences = validWorkExperiences;
            this.appFormUserValue.professionalCategory =
              this.appFormUserValue.professionalCategory.join(",");
            if (this.types == "add") {
              disciplineAdd(this.appFormUserValue)
                .then((res) => {
                  this.clickFlag = true;
                  this.$emit("closeshowDialog");
                })
                .catch((err) => {
                  this.clickFlag = true;
                });
            } else {
              disciplineEdit(this.appFormUserValue)
                .then((res) => {
                  this.clickFlag = true;
                  this.$emit("closeshowDialog");
                })
                .catch((err) => {
                  this.clickFlag = true;
                });
            }
          }
        }
      });
    },
    handleOptClose() {
      this.$emit("closeshowDialog");
    },
    handleDoFun2(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  },
};
</script>
<style scoped>
.orderTitle {
  text-align: left;
  padding-left: 20px;
}
.m-title2 {
  width: 100%;
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
  padding: 6px 0;
}
.mr5 {
  margin-right: 5px;
}
::v-deep .el-table__empty-block {
  min-height: 40px;
}
::v-deep .el-table__empty-text {
  line-height: 40px;
}
.m-title {
  width: 100%;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-right: 1px solid #ebebeb;
  margin-top: 6px;
  margin-bottom: 6px;
}
::v-deep .tableCustom .el-textarea__inner {
  overflow: hidden !important;
  padding: 0;
  font-size: 13px;
  min-height: 23px !important;
}

::v-deep .el-table__header-wrapper {
  height: auto;
}

::v-deep .el-radio-group {
  width: 150px;
  min-width: 150px;
}

::v-deep .el-radio__label {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
}
::v-deep .tableCustom .el-input__inner {
  height: 32px;
}
::v-deep .tableCustom .el-table__cell {
  padding: 8px 0;
}
::v-deep .tableCustom .el-input__prefix {
  top: -4px;
}
</style>

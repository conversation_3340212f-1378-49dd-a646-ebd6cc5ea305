import router from "./router";
import store from "./store";
import Cookies from "js-cookie";
import { Message } from "element-ui";
import { getToken } from "@/assets/js/auth";
import util from "@/assets/js/public";

const whiteList = ["/login"];
router.beforeEach((to, from, next) => {
	// 解决页面刷新参数丢失问题
	const query = {...to.query};
	let myQuery = {};
  if(Object.keys(query).length > 0){
		myQuery = {...to.query};
		if(myQuery.from){
			let myQueryNew = {...to.query};
			delete myQueryNew.from;
			// 将查询参数值存储在路由状态中
			Cookies.set("query", JSON.stringify(myQueryNew));
		}else{
			// 将查询参数值存储在路由状态中
			Cookies.set("query", JSON.stringify(myQuery));
		}
		
	}else{
		const cookieQuery = Cookies.get("query");
		if(cookieQuery){
			if(to.path == from.path){
				myQuery = JSON.parse(cookieQuery);
			}else{
				Cookies.remove("query");
			}
		}
	}
	
	if (myQuery.from) {
		store.dispatch("GetInfoSSO").then(function(res){
			if (router.options.routes.length > 3){
				router.options.routes.splice(3);
			}
			// store.dispatch('GetMenus').then(res => {
				if(!res.data) res.data = [];
				let datai = res.data.authPermissions.sort(function(a, b) {
						return a.displayOrder > b.displayOrder ? 1 : -1;
				});
				let datas = util.toTreeData(datai,"id","parentId","id,parentId,description,url,icon,menuLevel,permissionType,query");
				let dynamicRoutes = util.getRoutes(datas, 1);
				for (let i in dynamicRoutes) {
					router.options.routes.push(dynamicRoutes[i]);
				}
				store.state.user.menus = dynamicRoutes.length > 0 ? dynamicRoutes : router.options.routes;
				router.addRoutes(dynamicRoutes);            
				delete myQuery.from;
				myQuery.myFrom = "OA";//用于标识单点
				next({path: to.path,query: myQuery});
			// }).catch(error =>{
			// 	console.log('No menus');
			// });
		}).catch(function(err){
				next("/login");
		});
	}else{
		if (getToken()) {
			if (to.path === "/login") {
				next({ path: "/" });
			} else {
				if (store.getters.menus.length === 0) {
					store.dispatch("GetInfo").then(res => {
						if (router.options.routes.length > 3){
							router.options.routes.splice(3);
						}
						store.dispatch("GetMenus").then(ress => {
							if(!ress.data) ress.data = [];
							let datai = ress.data.sort(function(a, b) {
								return a.displayOrder > b.displayOrder ? 1 : -1;
							});
							let datas = util.toTreeData(datai,"id","parentId","id,parentId,description,url,icon,menuLevel,permissionType,query");
							let dynamicRoutes = util.getRoutes(datas, 1);
							for(let i in dynamicRoutes){
								router.options.routes.push(dynamicRoutes[i]);
							}
							store.state.user.menus = dynamicRoutes.length > 0 ? dynamicRoutes : router.options.routes;
							router.addRoutes(dynamicRoutes);
							next({ path: to.path,query: myQuery});
						}).catch(error => {
							//  console.log('No menus');
							next();
						})
					}).catch(err => {
						store.dispatch("FedLogOut").then(outs => {
							Message.error(err || "验证失效,请重新登录!");
							next({ path: "/" });
						});
					});
				} else {
					next();
				}
			}
		} else {    
			if (whiteList.indexOf(to.path) > -1) {
				next();
			} else {
				next("/login");
			}
		}
	}
});
router.afterEach(() => {
  // console.log("end");
});

<!--
  扩展信息表格组件
  功能：管理领导干部的各类扩展信息，包括16个不同类型的信息表格
  作者：系统生成
  创建时间：根据clueTable.vue模式重构
-->
<template>
  <div>
    <!-- 扩展信息主容器 -->
    <div class="message tableForm" style="margin-top: 0px">
      <div class="orderTitle" style="font-weight: 700">扩展信息</div>
      <!-- 表格1：领导干部拒收或上交礼金礼品情况 -->
      <div class="flex j-s a-c m-title">
        <span>领导干部拒收或上交礼金礼品情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('giftRefusal')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="giftRefusalList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.leaderName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.unitName"
              placeholder="请输入单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="职务" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.position"
              placeholder="请输入职务"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="拒收或上交时间" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.rejectOrHandoverTime"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择拒收或上交时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        
    
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'giftRefusal')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


       <div class="flex j-s a-c m-title">
        <span>领导干部从事或参与营利性活动或兼职持股信息</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('profitActivity')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="profitActivityList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
     
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.leaderName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.unitName"
              placeholder="请输入单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="职务" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.position"
              placeholder="请输入职务"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="涉及类型" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.involvedType"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in involveTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'profitActivity')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="flex j-s a-c m-title">
        <span>领导干部本人、配偶及共同生活子女住房情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('housing')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="housingList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.leaderName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.unitName"
              placeholder="请输入单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="职务" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.position"
              placeholder="请输入职务"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="房屋信息类别" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.involvedType"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in houseTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'housing')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


      <div class="flex j-s a-c m-title">
        <span>领导干部婚姻变化情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('marriageChange')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="marriageChangeList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.leaderName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.unitName"
              placeholder="请输入单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="职务" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.position"
              placeholder="请输入职务"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="配偶姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.spouseName"
              placeholder="请输入配偶姓名"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'marriageChange')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

       <div class="flex j-s a-c m-title">
        <span>领导干部参与操办本人及近亲属婚丧喜庆事宜情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('celebration')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="celebrationList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.leaderName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.unitName"
              placeholder="请输入单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="职务" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.position"
              placeholder="请输入职务"
            ></el-input>
          </template>
        </el-table-column>
          <el-table-column label="操办事项" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.eventType"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in eventTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'celebration')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


       <div class="flex j-s a-c m-title">
        <span>领导干部本人配偶、子女及其配偶出国（境）情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('abroad')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="abroadList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="出国（境）人员姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.abroadPersonName"
              placeholder="请输入出国（境）人员姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="称谓" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.appellation"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in appellationList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
         <el-table-column label="出国（境）情况类别" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.abroadCategory"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in abroadCategoryList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
         <el-table-column label="地点(子女配偶国籍)" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.locationOrNationality"
              placeholder="请输入地点(子女配偶国籍)"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'abroad')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


      <div class="flex j-s a-c m-title">
        <span>领导干部本人配偶、子女及其配偶受到查处或涉嫌犯罪情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('investigation')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="investigationList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="受查处或涉嫌犯罪人姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.suspectedPersonName"
              placeholder="请输入受查处或涉嫌犯罪人姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="与本人关系" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.relationship"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in appellationList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
         <el-table-column label="（原）工作单位及职务" align="center">
          <template v-slot:default="scope">
              <el-input
              size="mini"
              v-model="scope.row.originalEmployerAndPosition"
              placeholder="请输入（原）工作单位及职务"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="执纪执法机关" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.lawEnforcementAgency"
              placeholder="请输入执纪执法机关"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'investigation')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


      <div class="flex j-s a-c m-title">
        <span>领导干部配偶、子女及其配偶从业情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('employment')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="employmentList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.personName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="称谓" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.appellation"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in appellationList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
         <el-table-column label="从业类别" align="center">
          <template v-slot:default="scope">
               <el-select
              v-model="scope.row.employmentCategory"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in employmentCategoryList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'employment')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="flex j-s a-c m-title">
        <span>领导干部其他重大事项报告</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('majorReport')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="majorReportList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.leaderName"
              placeholder="请输入姓名"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.unitName"
              placeholder="请输入单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="职务" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.position"
              placeholder="请输入职务"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="报告事项" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.reportItem"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in reportItemList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'majorReport')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


       <div class="flex j-s a-c m-title">
        <span>领导干部经济责任审计情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('audit')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="auditList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="被审计人" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.auditedPerson"
              placeholder="请输入被审计人"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="现单位及职务" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.currentUnitAndPosition"
              placeholder="请输入现单位及职务"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="审计单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.auditUnit"
              placeholder="请输入审计单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="审计时间" align="center">
          <template v-slot:default="scope">
             <el-date-picker
              v-model="scope.row.auditTime"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择审计时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'audit')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


      <div class="flex j-s a-c m-title">
        <span>因不如实报告个人有关事项受到处理的情况</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('handling')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="handlingList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="处理时间" align="center">
          <template v-slot:default="scope">
             <el-date-picker
              v-model="scope.row.handlingTime"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择处理时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>
         <el-table-column label="处理部门" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.handlingDepartment"
              placeholder="请输入处理部门"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="处理事由" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.handlingReason"
              placeholder="请输入处理事由"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="处理结果" align="center">
          <template v-slot:default="scope">
             <el-select
              v-model="scope.row.handlingResult"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in handlingResultList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
          <el-table-column label="附件数量" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.attachmentCount"
              placeholder="请输入附件数量"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'handling')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


      <div class="flex j-s a-c m-title">
        <span>班子成员述职述廉材料</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('dutyReport')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="dutyReportList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="述职述廉年度" align="center">
          <template v-slot:default="scope">
              <el-select
              v-model="scope.row.dutyReportYear"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in reportYearList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
         <el-table-column label="述职述廉材料标题" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.dutyReportTitle"
              placeholder="请输入述职述廉材料标题"
            ></el-input>
          </template>
        </el-table-column>
          <el-table-column label="附件数量" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.attachmentCount"
              placeholder="请输入附件数量"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'dutyReport')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


       <div class="flex j-s a-c m-title">
        <span>巡察报告中对领导班子成员的基本评价部分</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('inspectionReport')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="inspectionReportList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="巡察年度" align="center">
          <template v-slot:default="scope">
              <el-select
              v-model="scope.row.inspectionYear"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in reportYearList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
         <el-table-column label="被巡察单位" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.inspectedUnit"
              placeholder="请输入被巡察单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="巡查报告标题" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.inspectionReportTitle"
              placeholder="请输入巡查报告标题"
            ></el-input>
          </template>
        </el-table-column>
          <el-table-column label="附件数量" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.attachmentCount"
              placeholder="请输入附件数量"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'inspectionReport')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>



      <div class="flex j-s a-c m-title">
        <span>民主生活会对照检查材料</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('democraticLife')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="democraticLifeList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="民主生活会年度" align="center">
          <template v-slot:default="scope">
              <el-select
              v-model="scope.row.democraticLifeYear"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in reportYearList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
         <el-table-column label="民主生活会检查材料标题" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.checkMaterialTitle"
              placeholder="请输入民主生活会检查材料标题"
            ></el-input>
          </template>
        </el-table-column>
          <el-table-column label="附件数量" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.attachmentCount"
              placeholder="请输入附件数量"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'democraticLife')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="flex j-s a-c m-title">
        <span>活情况信息</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('activityInfo')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="activityInfoList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="管理问责信息" align="center">
          <template v-slot:default="scope">
              <el-input
              size="mini"
              v-model="scope.row.managementAccountabilityInfo"
              placeholder="请输入管理问责信息"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="	评价信息" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.evaluationInfo"
              placeholder="请输入评价信息"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'activityInfo')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


       <div class="flex j-s a-c m-title">
        <span>其他反应廉政情况的材料</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('otherIntegrity')" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="otherIntegrityList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="其他反映廉政情况的材料" align="center">
          <template v-slot:default="scope">
              <el-input
              size="mini"
              v-model="scope.row.otherIntegrityMaterial"
              placeholder="请输入其他反映廉政情况的材料"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="附件数量" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.attachmentCount"
              placeholder="请输入附件数量"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index, 'otherIntegrity')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

          <div class="flex j-s a-c m-title">
            <span>附件</span>
           
          </div>
          <div class="message tableForm">

           <sb-el-form
            ref="formfile"
            :form="formFile"
            v-model="formFileValue"
            @uploadFileList="uploadFileList"
            :disabled="formFileValue.formDisabled"
            :on-ok="handleDoFunFile"
          >
          </sb-el-form>
        </div>



      
    </div>
  </div>
</template>
<script>
import { uploadProcessFiles } from "@/api/public";
import { getDictList } from "@/api/public";

export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      giftRefusalList: [], // 领导干部拒收或上交礼金礼品情况
      profitActivityList: [], // 领导干部从事或参与营利性活动或兼职持股信息
      housingList: [], // 领导干部本人、配偶及共同生活子女住房情况
      marriageChangeList: [], // 领导干部婚姻变化情况
      celebrationList: [], // 领导干部参与操办本人及近亲属婚丧喜庆事宜情况
      abroadList: [], // 领导干部本人配偶、子女及其配偶出国（境）情况
      investigationList: [], // 领导干部本人配偶、子女及其配偶受到查处或涉嫌犯罪情况
      employmentList: [], // 领导干部配偶、子女及其配偶从业情况
      majorReportList: [], // 领导干部其他重大事项报告
      auditList: [], // 领导干部经济责任审计情况
      handlingList: [], // 因不如实报告个人有关事项受到处理的情况
      dutyReportList: [], // 班子成员述职述廉材料
      inspectionReportList: [], // 巡察报告中对领导班子成员的基本评价部分
      democraticLifeList: [], // 民主生活会对照检查材料
      activityInfoList: [], // 活情况信息
      otherIntegrityList: [], // 其他反应廉政情况的材料
    
      involveTypeList: [], // 涉及类型
      houseTypeList: [], // 房屋信息类别
      eventTypeList: [], // 操办事项
      abroadCategoryList: [], // 出国（境）情况类别
      employmentCategoryList: [], // 从业类别
      reportItemList: [], // 报告事项
      handlingResultList: [], // 处理结果
      reportYearList: [], // 年度
      appellationList: [], // 称谓
      formFileValue:{},
      formFile:{
         formDisabled: false,
        labelWidth: "150px",
        inline: true,
        formItemList:[
           {
            class: "c12",
            label: "附件",
            key: "extendFileLIst",
            type: "sbUpload",
            btnText: "+",
            fun: "uploadFileList",
            listType: "text",
            multiple: true,
          },
        ]
      }

    };
  },
  mounted() {
    this.getDictList();
  },
  methods: {
//     involveType --> 涉及类型
// houseType --> 房屋信息类别
// eventType --> 操办事项
// abroadCategory --> 出国（境）情况类别
// employmentCategory --> 从业类别
// reportItem --> 报告事项
// handlingResult --> 处理结果
// reportYear --> 年度
    getDictList() {
      getDictList('involveType').then((res) => {
        this.involveTypeList = res.data;
      });
      getDictList('houseType').then((res) => {
        this.houseTypeList = res.data;
      });
      getDictList('eventType').then((res) => {
        this.eventTypeList = res.data;
      });
      getDictList('abroadCategory').then((res) => {
        this.abroadCategoryList = res.data;
      });
      getDictList('employmentCategory').then((res) => {
        this.employmentCategoryList = res.data;
      });
      getDictList('reportItem').then((res) => {
        this.reportItemList = res.data;
      });
      getDictList('handlingResult').then((res) => {
        this.handlingResultList = res.data;
      });
      getDictList('reportYear').then((res) => {
        this.reportYearList = res.data;
      });
      getDictList('appellation').then((res) => {
        this.appellationList = res.data;
      });
    },
    handleAdd(type) {
      let obj = {};

      if (type === 'giftRefusal') {
        obj = {
          leaderName: '',
          unitName: '',
          position: '',
          rejectOrHandoverTime: ''
        };
        this.giftRefusalList.push(obj);
      } else if (type === 'profitActivity') {
        obj = {
          leaderName: '',
          unitName: '',
          position: '',
          involvedType: ''
        };
        this.profitActivityList.push(obj);
      } else if (type === 'housing') {
        obj = {
          leaderName: '',
          unitName: '',
          position: '',
          involvedType: ''
        };
        this.housingList.push(obj);
      } else if (type === 'marriageChange') {
        obj = {
          leaderName: '',
          unitName: '',
          position: '',
          spouseName: ''
        };
        this.marriageChangeList.push(obj);
      } else if (type === 'celebration') {
        obj = {
          leaderName: '',
          unitName: '',
          position: '',
          organizingMatter: ''
        };
        this.celebrationList.push(obj);
      } else if (type === 'abroad') {
        obj = {
          abroadPersonName: '',
          appellation: '',
          abroadCategory: '',
          locationOrNationality: ''
        };
        this.abroadList.push(obj);
      } else if (type === 'investigation') {
        obj = {
          suspectedPersonName: '',
          relationship: '',
          originalEmployerAndPosition: '',
          lawEnforcementAgency: ''
        };
        this.investigationList.push(obj);
      } else if (type === 'employment') {
        obj = {
          personName: '',
          appellation: '',
          employmentCategory: ''
        };
        this.employmentList.push(obj);
      } else if (type === 'majorReport') {
        obj = {
          spouseName: '',
          schoolName: '',
          position: '',
          reportedMatter: ''
        };
        this.majorReportList.push(obj);
      } else if (type === 'audit') {
        obj = {
          auditedPerson: '',
          currentUnitAndPosition: '',
          auditUnit: '',
          auditTime: ''
        };
        this.auditList.push(obj);
      } else if (type === 'handling') {
        obj = {
          handlingTime: '',
          handlingDepartment: '',
          handlingReason: '',
          handlingResult: '',
          attachmentCount: ''
        };
        this.handlingList.push(obj);
      } else if (type === 'dutyReport') {
        obj = {
          dutyReportYear: '',
          dutyReportTitle: '',
          attachmentCount: ''
        };
        this.dutyReportList.push(obj);
      } else if (type === 'inspectionReport') {
        obj = {
          inspectionYear: '',
          inspectedUnit: '',
          inspectionReportTitle: '',
          attachmentCount: ''
        };
        this.inspectionReportList.push(obj);
      } else if (type === 'democraticLife') {
        obj = {
          democraticLifeYear: '',
          checkMaterialTitle: '',
          attachmentCount: ''
        };
        this.democraticLifeList.push(obj);
      } else if (type === 'activityInfo') {
        obj = {
          managementAccountabilityInfo: '',
          evaluationInfo: ''
        };
        this.activityInfoList.push(obj);
      } else if (type === 'otherIntegrity') {
        obj = {
          otherIntegrityMaterial: '',
          attachmentCount: ''
        };
        this.otherIntegrityList.push(obj);
      }
    },

    handledelxueli(index, type) {
      if (type === 'giftRefusal') {
        this.giftRefusalList.splice(index, 1);
      } else if (type === 'profitActivity') {
        this.profitActivityList.splice(index, 1);
      } else if (type === 'housing') {
        this.housingList.splice(index, 1);
      } else if (type === 'marriageChange') {
        this.marriageChangeList.splice(index, 1);
      } else if (type === 'celebration') {
        this.celebrationList.splice(index, 1);
      } else if (type === 'abroad') {
        this.abroadList.splice(index, 1);
      } else if (type === 'investigation') {
        this.investigationList.splice(index, 1);
      } else if (type === 'employment') {
        this.employmentList.splice(index, 1);
      } else if (type === 'majorReport') {
        this.majorReportList.splice(index, 1);
      } else if (type === 'audit') {
        this.auditList.splice(index, 1);
      } else if (type === 'handling') {
        this.handlingList.splice(index, 1);
      } else if (type === 'dutyReport') {
        this.dutyReportList.splice(index, 1);
      } else if (type === 'inspectionReport') {
        this.inspectionReportList.splice(index, 1);
      } else if (type === 'democraticLife') {
        this.democraticLifeList.splice(index, 1);
      } else if (type === 'activityInfo') {
        this.activityInfoList.splice(index, 1);
      } else if (type === 'otherIntegrity') {
        this.otherIntegrityList.splice(index, 1);
      }
    },

    uploadFileList(obj) {
      uploadProcessFiles(obj.formData)
        .then((res) => {
          obj.content.onSuccess(res, obj.content.file, []);
        })
        .catch((error) => {
          obj.content.onError();
        });
    },

    handleAddFamily() {
      this.familyList.push({
        name: "",
        relation: "",
        phone: "",
        email: "",
        address: "",
      });
    },

    handleDoFunFile(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  },
};
</script>
<style scoped>
.orderTitle {
  text-align: left;
  padding-left: 20px;
}
.m-title {
  width: 100%;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-right: 1px solid #ebebeb;
  margin-top: 6px;
  margin-bottom: 6px;
}
.m-title2 {
  width: 100%;
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
  padding: 6px 0;
}
::v-deep .el-button--text {
  color: rgba(192, 0, 0, 1);
}
::v-deep .tableCustom .el-input__prefix {
  top: 3px !important;
}
::v-deep .tableCustom .el-table__cell {
  padding: 4px 0 !important;
}
.mr5 {
  margin-right: 5px;
}
::v-deep .el-table__empty-block {
  min-height: 40px !important;
}
::v-deep .el-table__empty-text {
  line-height: 40px !important;
}
</style>

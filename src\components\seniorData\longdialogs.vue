<template>
  <div class="w100 inputBtn">
    <el-input ref="elInput" :type="item.inputType || 'text'" v-bind="$attrs" v-on="$listeners" :size="item.size || 'small'" :placeholder="item.placeholder || item.label || '请输入'" :disabled="item.disabled || false" :readonly="item.readonly || false" :autosize="item.autosize || false">
      <el-button slot="append" :size="item.size || 'small'" type="primary" :disabled="item.disabled || false" @click="openDialog">{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>

    <el-dialog title="选择数据" v-dialogDrag :visible.sync="dialogVisible" width="60%" append-to-body>
      <el-container>
        <el-main>
          <sb-el-table :table="table" ref="chooseGroup" v-bind="$attrs" v-on="$listeners" @getList="getList" @updateTableData="updateTableData"></sb-el-table>
        </el-main>
        <el-aside width="200px" class="asideR">
          <h5 class="fbold">已选数据</h5>
          <div class="chooseD">
            <div v-for="citem in table.multipleSelection" :key="citem[item.dialogData.stores]">{{ citem[item.dialogData.stores]
              }}
              <!-- <span class="fr" @click="delChoose(citem)"><svg-icon iconClass="close"></svg-icon></span> -->
            </div>
          </div>
        </el-aside>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="small">确定</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import { getDataBySuperApi, getDataBySuperApiNoPage, getDataByApiPage } from '@/api/public'
import {
  getTableList,
  getTableListNoPage
} from '@/api/public'
import util from "@/assets/js/public";
import request from "@/assets/js/request";
import store from "@/store";
export default {
  name: "longdialogs",
  props: {
    item: {
      type: Object,
      required: true
    },
    appFormValue: {
      type: Object,
    },
    dialogData: {
      type: Object,
    }
  },
  data() {
    return {
      dialogVisible: false,
      table: {
        modulName: "dialogs-选择数据", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: true, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            { label: "关键词", key: "appName", type: "input" },
            // {label: "流程类型",key: "pmInstType",type: "select",dictType: "processType"}
          ],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {

  },
  methods: {
    openDialog(data) {
      this.getTableInfo()
      //console.log(this.$refs.elInput);
      let inputData = this.$refs.elInput._props;
      let formData = this.$refs.elInput.elForm._props.model;
      //console.log(JSON.stringify(formData));
      //console.log('formData');
      let relevancy = this.item.relevancy.split(",");
      let di = formData[relevancy[0].split("-")[0]]
        ? formData[relevancy[0].split("-")[0]].split(",").length
        : 0;
      if (di === 0) {
        this.table.multipleSelection = [];
      }
      for (let i = 0; i < di; i++) {
        let datai = {};
        for (let j in relevancy) {
          var reF = relevancy[j].split("-");
          if (reF.length === 1) reF.push(reF[0]);
          datai[reF[1]] = formData[reF[0]]
            ? formData[reF[0]].split(",")[i]
            : "";
        }
        var ml = 0;
        for (let k in this.table.multipleSelection) {
          for (let j in relevancy) {
            var reF = relevancy[j].split("-");
            if (reF.length === 1) reF.push(reF[0]);
            if (datai[reF[1]] === this.table.multipleSelection[k][reF[1]]) ml++;
          }
        }
        if (ml !== relevancy.length) {
          this.table.multipleSelection.push(datai);
        }
      }
      if (this.$refs.chooseGroup) {
        this.$refs.chooseGroup.handleTableMultipSelection();
      }
      this.dialogVisible = true
    },
    getTableInfo() {
      if (this.dialogData.options.length > 0) {
        const options = this.dialogData.options.map(element => {
          let option = {
            id: element.value,
            label: element.name,
            prop: element.value,
          };
          if(element.labelWidth) { option.width = element.labelWidth; }
          if(element.paramType) { option.align = element.paramType }
          return option;
        });
        this.table.tr = options
      } else {
        this.table.tr = []
      }

      if (this.dialogData.paramsArr&&this.dialogData.paramsArr.length > 0) {
        const formItemList = this.dialogData.paramsArr.map(element => {
          let searchInfo = {
            type: element.type?element.type:"input",
            label: element.name,
            prop: element.value,
            key: element.value
          };
          if(searchInfo.type=='1') { searchInfo.type = 'input'; }
          if(element.labelWidth) { searchInfo.labelWidth = element.labelWidth+'px'; }
          if (element.type == 'select') {
            if(element.selectype == 'async') {
              let params = {}
              if(element.protParamName){
                params[element.protParamName] = element.protParam
              }
              searchInfo.apiUrl = element.sourceFun,
              searchInfo.request = element.request,
              searchInfo.paramsObj = params
            } else if(element.selectype == 'fixed') {
              searchInfo.options = element.options
            } else if (element.selectype == 'dictType'){ 
              searchInfo.dictType = element.dictType
              searchInfo.options = ''
            }
          }
          if (['radio', 'multiple'].includes(element.type)) {
            searchInfo.type = 'select'
            if (element.type === 'multiple') {
              searchInfo.multiple = true
            }
            if(element.selectype == 'async'){
              let params = {}
              if(element.protParamName){
                params[element.protParamName] = element.protParam
              }
              searchInfo.apiUrl = element.sourceFun,
              searchInfo.request = element.request,
              searchInfo.paramsObj = params
            } else if (element.selectype == 'dictType'){ 
              formInfo.dictType = element.dictType
              formInfo.options = ''
            } else {
              searchInfo.options = element.options
            }
          }
          if (element.type == 'date') {
            searchInfo.valueFormat = element.valueFormat
            if(element.valueFormat == 'yyyy-MM' || element.valueFormat == 'yyyyMM'){
              searchInfo.subtype = 'month'
            }
            if(element.valueFormat == 'yyyy'){
              searchInfo.subtype = 'year'
            }
          }
          if(element.type == 'date2'){
            searchInfo.type = 'date'
            searchInfo.subtype = 'datetimerange'
            searchInfo.valueFormat = element.valueFormat
            searchInfo.datetimerange = element.valueFormat
          } 
          if (element.required && element.defaultValue) {
            this.table.listQuery[element.value] = element.defaultValue
          }
          if(element.paramsType) {
            if(element.paramsType=='1') {
              this.table.listQuery[element.value] = element.paramsValue;
            } else if(element.paramsType=='2') {
              this.table.listQuery[element.value] = this.$store.getters.user[element.paramsValue]
            } else {
              this.table.listQuery[element.value] = this.appFormValue[element.paramsValue]
            }
          }
          if (element.hiddenParams) {
            // if(!element.paramsType){
            //   this.table.listQuery[element.value] = element.paramsValue
            // }
          }else{
            return searchInfo;
          }
        });
        this.table.queryForm.formItemList = formItemList
      } else {
        this.table.queryForm.formItemList = []
      }
      this.getList();
    },
    // 查询列表
    getList() {
      if (this.dialogData.dialogType == '1') {
        this.table.listQuery.url = this.dialogData.dialogData.url
        if (this.dialogData.isPage) {
          this.table.loading = true;
          getDataBySuperApi(this.table.listQuery).then((res) => {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.data.content;
            this.table.total = JSON.stringify(res.data) == '{}' ? null : res.data.data.totalElements
          }).catch((err) => {
            this.table.loading = false;
          });
        } else {
          this.table.loading = true;
          getDataBySuperApiNoPage(this.table.listQuery).then((res) => {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.data;
          }).catch((err) => {
            this.table.loading = false;
          });
        }
      } else if (this.dialogData.dialogType == '2') {
        if (Object.keys(this.dialogData.api).length > 0) {
          this.getInfo()
          // .then((res) => {
          //   if (res.status === 200) {
          //     this.table.loading = false;
          //     this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.content;
          //     this.table.total = JSON.stringify(res.data) == '{}' ? null : res.data.totalElements
          //   }
          // })
        }
      } else {
        let database = this.util.toHump(this.dialogData.database);
        let keyArr = Object.keys(this.table.listQuery);
        let paramsList = {};
        keyArr.forEach(key => {
          if(key!='page'&&key!='size') {
            this.dialogData.paramsArr.forEach((item) => {
              if(item.value==key) {
                let val = this.table.listQuery[key];
                paramsList[key] = {
                  paramValue: val,
                  paramType: item.paramType
                }
              }
            })
          }
        })
        paramsList.page = this.table.listQuery.page;
        paramsList.size = this.table.listQuery.size;
        if(this.dialogData.isPage) {
          getTableList(
            process.env.VUE_APP_APPCODE,
            database,
            paramsList
          ).then((res) => {
            this.table.loading = false;
            this.table.data = res.data.content
            this.table.total = res.data.totalElements
          }).catch((err) => {
            this.table.loading = false;
          });
        } else {
          delete paramsList.page;
          delete paramsList.size;
          getTableListNoPage(
            process.env.VUE_APP_APPCODE,
            database,
            paramsList
          ).then((res) => {
            this.table.loading = false;
            this.table.data = res.data;
            // this.table.total = res.data.totalElements
          }).catch((err) => {
            this.table.loading = false;
          });
        }
      }
    },
    handelAPI() {
      const { type, reqUrl } = this.dialogData.api;
      let params = {};
      if(this.dialogData.api.params.length>0) {
        this.dialogData.api.params.forEach((item) => {
          if(item.value) {
            params[item.key] = item.value;
          }
        });
      }
      let data = {};
      if (this.dialogData.api.radio === "form-data") {
        if (this.dialogData.api.body.listValue && this.dialogData.api.body.listValue.length > 0) {
          const listValue = JSON.parse(JSON.stringify(this.dialogData.api.body.listValue));
          listValue.forEach((item) => {
            if (item.key && item.value) {
              data[item.key] = item.value
            }
          });
        }
      } else {
        if (this.dialogData.api.body.jsonValue) {
          const list = JSON.parse(JSON.stringify(this.dialogData.api.body.jsonValue));
          list.forEach((item) => {
            if (item.key && item.value) {
              data[item.key] = item.value
            }
          });
        }
      }
      return {
        type,
        reqUrl,
        params,
        data
      }
    },
    getInfo() {
      const { type, reqUrl, params, data } = this.handelAPI()
      // Object.assign(params, this.table.listQuery)
      if(this.dialogData.isPage) {
        let params1 = {
          url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}?page=${this.table.listQuery.page}&size=${this.table.listQuery.size}`),
          params: Object.assign(this.dialogData.api.param=='params'?params:data, this.table.listQuery)
        }
        getDataByApiPage(params1).then(res => {
          if (res.status === 200) {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.content;
            this.table.total = JSON.stringify(res.data) == '{}' ? null : res.data.totalElements
          }
        })
      } else {
        delete this.table.listQuery.page;
        delete this.table.listQuery.size;
        let params1 = {
          url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}`),
          params: Object.assign(this.dialogData.api.param=='params'?params:data, this.table.listQuery)
        }
        getDataByApiPage(params1).then(res => {
          if (res.status === 200) {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data;
          }
        })
      }
      // return request({
      //   method: type,
      //   url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}`),
      //   params: params,
      //   data: data,
      //   contentType: "application/json;charset=UTF-8",
      // });
    },

    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("chooseData", this.table.multipleSelection);
    },
    delChoose(row) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.table.multipleSelection));
        for (let i in arry) {
          let fl = 0;
          for (let j in row) {
            if (arry[i][j] === row[j]) fl++;
          }
          if (fl === row.length) arry.splice(arry[i], 1);
        }
        this.table.multipleSelection = arry;
        for (let i in this.table.data) {
          let fl = 0;
          for (let a in row) {
            if (this.table.data[i][a] && row[a] === this.table.data[i][a]) {
              fl++;
            }
          }
          if (fl === this.item.relevancy.split(",").length) {
            this.$refs.chooseGroup.handleToggleRowSelection(
              this.table.data[i],
              false
            );
          }
        }
      } else {
        //单选
        this.table.multipleSelection = [];
        this.$refs.chooseGroup.handleClearSelection();
      }
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
</style>
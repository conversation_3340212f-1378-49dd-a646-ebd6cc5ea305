<template>
  <div class="app-container">
    <div style="display: flex; height: 100%">
      <div class="container-left">
        <div class="box-card">
          <div class="clearfix">
            <span>组织机构</span>
          </div>
          <div class="tree-container">
            <el-tree
              :key="refreshTree"
              class="tree1"
              :props="defaultProps"
              ref="chooseOrgTree"
              :load="loadNodeOrg"
              lazy
              :default-expanded-keys="treeExpandData"
              :node-key="'id'"
              :highlight-current="true"
              :check-on-click-node="true"
            >
              <template #default="{ node, data }">
                <div
                  @click="handleNodeClick(node, data)"
                  style="width: 100%; height: 100%; line-height: 26px"
                >
                  {{ data.orgName }}
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="container-right">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 15px;
          "
        >
          <div class="clearfix2">
            <span>郑州市分公司 - 人员列表</span>
          </div>
          <div style=" display: flex;
            justify-content: space-between;
            align-items: center;">
            <el-button type="primary" size="small" @click="handleAddData()"
              >新增</el-button
            >
            <el-upload 
            class="upload-demo ml10 mr10"
            :action="uploadData.action"
            :on-success="uploadData.isPopup?uploadData.isDialog?handleDialog:handlePopup:handleSuccess"
            :before-upload="handleProgress"
            multiple
            :limit="uploadData.limit"
            :show-file-list="false"
            ref="upload">
            <el-button :key="uploadData.id" :size="uploadData.size || 'small'" type="primary">{{uploadData.name}}</el-button>
        </el-upload>
            <el-button type="primary" size="small" @click="handleExport()"
              >导出</el-button
            >
          </div>
        </div>

        <sb-el-table
          :table="table"
          @getList="getList"
          @handleAddData="handleAddData"
          @handleUpData="handleUpData"
          @handleRead="handleRead"
          @handleDelete="handleDelete"
           :on-ok="handleDoFun"
        >
        </sb-el-table>
      </div>
    </div>
     <el-dialog :title="title" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <addUser :key="cKey" :types="types" :rowData="rowdata"  @closeshowDialog="closeshowDialog"></addUser>
    </el-dialog>
  </div>
</template>

<script>
import addUser from "@/components/myCom/addUser";
import { getUserBaseList,disciplineDel } from "@/api/home";
export default {
  name: "EmployeeManagement",
  components:{
    addUser
  },
  data() {
    return {
        title:'',
        cKey:0,
        types:'',
        viewD:false,
        rowdata:{},
      refreshTree: 0,
      defaultProps: {
        children: "children",
        label: "orgName",
        isLeaf: "leaf",
      },
      treeExpandData: [],
      currentOrgId: null,
      table: {
        modulName: "员工管理",
        border: true,
        loading: false,
        stripe: true,
        hasSelect: false,
        showIndex: true,
        data: [
          {
            id: 1,
            name: '张三',
            age: 18,
            gender: '男',
            currentPosition: '开发工程师',
            employeeCode: '001',
            politicalStatus: '党员',
            nation: '汉族',
          }
        ],
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false,
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [],
        },
        tr: [
          {
            id: "employeeCode",
            label: "员工编号",
            prop: "employeeCode",
            width: 150,
          },
          { id: "name", label: "姓名", prop: "name", width: 150 },
          {
            id: "gender",
            label: "性别",
            prop: "gender",
            width: 150,
            dictType: "gender",
          },
          { id: "age", label: "年龄", prop: "age", width: 150 },
          { id: "nation", label: "民族", prop: "nation", width: 150 },
          {
            id: "politicalStatus",
            label: "政治面貌",
            prop: "politicalStatus",
            width: 150,
            dictType: "political_status",
          },
          { id: "currentPosition", label: "现任职务", prop: "currentPosition" },
        ],
        multipleSelection: [],
        dialogVisible: false,
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: false,
          formItemList: [],
        },
        hasOperation: true,
        operation: {
          width: "150",
          fixed: "right",
          data: [
             {
              id: "handleRead",
              name: "查看",
              fun: "handleRead",
              type: "primary",
            },
            {
              id: "handleUpData",
              name: "编辑",
              fun: "handleUpData",
              type: "primary",
            },
            {
              id: "handleDelete",
              name: "删除",
              fun: "handleDelete",
              type: "primary",
            },
          ],
        },
        hasOtherQueryBtn: false,
        otherQueryBtn: {
          data: [
          
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false,
        batchOperate: {},
      },
        uploadData: {
          id: "b",
          action: `/${process.env.VUE_APP_APPCODE}/action/branch/subInfo/importExcel?source=PC`,
          name: "导入",
          isPopup: false,
          isDialog: false,
        },
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    // 加载组织机构树节点
    loadNodeOrg(node, resolve) {
      // 这里调用API获取组织机构数据
      // 示例代码，需要根据实际API调整
      if (node.level === 0) {
        setTimeout(() => {
        const data = [
          { id: 1, orgName: "总公司", leaf: true },
          { id: 2, orgName: "开发中心公司", leaf: true },
          { id: 3, orgName: "湖南中心公司", leaf: true },
          // ... 更多组织机构数据
        ];
        resolve(data);
      }, 500);
        }
        if (node.level > 1) return resolve([]);

      
    },

    // 点击组织机构节点
    handleNodeClick(node, data) {
      console.log(node,data,'555')
      this.currentOrgId = data.id;
      this.table.listQuery.orgId = data.id;
      // this.getList();
    },

    // 获取员工列表
    getList(listQuery) {
      this.table.loading = true;
      getUserBaseList(this.table.listQuery).then(res => {
        this.table.data = res.data.content;
        this.table.total = res.data.total;
        this.table.loading = false;
      })
    },

    // 新增员工
    handleAddData() {
      // 新增逻辑
        this.cKey++;
      this.viewD = true;
      this.title = '新增人员信息'
      this.types = 'add'

    },

    // 编辑员工
    handleUpData(data) {
      // 编辑逻辑
       this.rowdata = data.row
      this.cKey++;
      this.viewD = true;
      this.title = '修改人员信息'
      this.types = 'edit'
    },
    // 查看
    handleRead(data) {
      // 编辑逻辑
       this.rowdata = data.row
      this.cKey++;
      this.viewD = true;
      this.title = '查看人员信息'
      this.types = 'read'
    },

    // 删除员工
    handleDelete(data) {
      // 删除逻辑
       this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(() => {
        disciplineDel({id: data.row.id}).then(res => {
          this.getList()
        })
      }).catch(() => {

      });
    },

     handleProgress(){
        // this.table.tableLoading = true
    },

    handleExport() {
      exportParameter(this.listQuery).then((res) => {
        if (res.data) {
          this.util.blobDownload(res.data, res.filename);
        } else {
          this.$message({
            message: "导出失败",
            type: "warning",
            duration: 1500,
          });
        }
      });
    },

    handleSuccess(response,file) {
      console.log(response)
      if(response.data && response.status==200){
        this.$message({
            type:'success',
            message:'导入成功'
        })
        console.log(response.data)
           
      }else{
         this.$message.error(response.message);
      }
    },
    closeshowDialog(){
        this.viewD = false
    },
     handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  },
};
</script>

<style scoped>
.app-container {
  height: calc(100vh - 84px);
}

.container-left {
  width: 15%;
  height: 100%;

}

.container-right {
  width: 85%;
}

.tree-container {
  height: calc(100% - 84px);
  overflow-y: auto;
  border-right: 1px solid #e0e0e0;
  padding-top: 15px;

}

.box-card {
  height: 100%;
}

::v-deep .el-card__body {
  padding: 10px;
}
.clearfix {
  font-weight: bold;
  /* margin-bottom: 15px; */
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-top: 10px;
}
.clearfix2 {
  font-weight: bold;

  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-top: 10px;
}
</style>
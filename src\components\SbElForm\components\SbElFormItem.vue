<template>
    <div :class="item.show === false?'hide':item.inline?'inlineB ' + (item.class || ''):''">
    <!-- <div class="inlineB" v-bind:class="classObject" :class="item.class"> -->
            <!-- {{item.class}} -->
            <!-- generalPage: 通用类，不显示label。formPage：表单页面，显示label -->
        <el-form-item
            :label="currentLabel"
            :prop="item.key"
            :label-width="lw"
        >
        <!--展示文本
        <template v-if="item.type==='text'" v-slot="scope">
            <span v-text="scope">aaaaa</span>
        </template>-->
            <!--文本框/多选文本框-->
            <el-input
                v-if="item.type === 'input'"
                :type="item.inputType || 'text'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                @input="handleChange"
                @blur="handleBlur"
                :placeholder="item.placeholder || '请输入' + item.label || '请输入'"
                :disabled="item.disabled || false"
                :readonly="item.readonly || false"
                :clearable="item.clearable || false"
                :autosize="item.autosize || {minRows: 2}"
                :maxlength="(item.rule&&item.rule.maxlength)?item.rule.maxlength:''"
                :show-word-limit="true"
            >
                <template v-if="item.append" slot="append">{{
                    item.append
                }}</template>
                <template v-if="item.prepend" slot="prepend">{{
                    item.prepend
                }}</template>
                <template v-if="item.prefix" slot="prefix">{{
                    item.prefix
                }}</template>
                <template v-if="item.suffix" slot="suffix">{{
                    item.suffix
                }}</template>
            </el-input>
            <!--富文本-->
            <sb-kindeditor
                v-else-if="item.type === 'editor'"
                ref="kindeditor"
                :height="item.height || '280'"
                :disabled="item.disabled || false"
                :content="keyVal"
                @input="getContent"
            ></sb-kindeditor>
			<!-- tinymce 富文本编辑器 -->
			<sb-tinymce
				v-else-if="item.type === 'tinymce'"
				ref="tinymceeditor"
				:width="item.width || '100%'"
				:height="item.height || '300'"
				:disabled="item.disabled || false"
				:content="keyVal"
				@input="getContent">
			</sb-tinymce>
			<!-- 时间状态控件 -->
			<sb-timestatus
				v-else-if="item.type === 'timestatus'"
				ref="timestatus"
				:findAll="item.findAll"
				:startTime="item.startTime"
				:endTime="item.endTime"
				:timeInterval="item.timeInterval">
			</sb-timestatus> 
			<!-- 评分组件 -->
			<sb-score
				v-else-if="item.type === 'score'"
				ref="score"
				:options="ajaxOptions"
				:findAll="item.findAll"
                :item="item"
                @handleScoreData="handleScoreData">
			</sb-score>
            <!--文本框/多选文本框(带按钮，按钮有单击事件)-->
            <el-input
                v-else-if="item.type === 'input-btn'"
                class="inputBtn"
                :type="item.inputType || 'text'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                :placeholder="item.inputPlaceholder || '请点击按钮选择'"
                :readonly="!!item.inputReadonly ? true : false"
                :disabled="item.disabled || false"
                :autosize="item.autosize || false"
            >
                <el-button
                    slot="append"
                    :size="item.size || 'mini'"
                    type="primary"
                    :disabled="item.disabled || false"
                    @click="handleInputBtn"
                    >{{ item.btnText }}
                    <svg-icon
                        v-if="!item.btnText"
                        iconClass="sousuo"
                    ></svg-icon>
                </el-button>
            </el-input>
            <!--文本框/多选文本框(带按钮，按钮有单击事件)-->
            <sb-el-input-upload
                v-else-if="item.type === 'input-upload'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :on-ok="handleDoFun"
                @uploadHttpRequest="uploadHttpRequest"
            ></sb-el-input-upload>
            <!--文本框/多选文本框(带选择查询)-->
            <sb-choose-username
                v-else-if="item.type === 'chooseUsername'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                @chooseData="handleChooseData"
            ></sb-choose-username>
            <!--文本框/多选文本框(带选择查询)-->
            <sb-choose-teacher
                v-else-if="item.type === 'chooseTeacher'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                @chooseData="handleChooseData"
            ></sb-choose-teacher>
            <!--文本框/多选文本框(树)-->
            <sb-choose-org
                v-else-if="item.type === 'chooseOrg'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                @chooseData="handleChooseData"
                @uploadHttpRequest="uploadHttpRequest"
            ></sb-choose-org>
            <!--数字框-->
            <el-input-number
                v-else-if="item.type === 'number'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                @change="handleChange"
                :placeholder="item.placeholder || '请输入'"
                :disabled="item.disabled || false"
                :readonly="item.readonly || false"
                :min="item.min || 0"
                :max="item.max"
                :step="item.step || 1"
                :precision="item.precision"
            ></el-input-number>
            <!--下拉框-->
            <el-select
                v-else-if="item.type === 'select'"
                v-bind="$attrs"
                :filter-method="item.filterMethod ? filterMethod : null"
                :filterable="item.filterable || item.filterMethod ? true : false"
                v-on="$listeners"
                :size="item.size || 'mini'"
                @change="handleChange"
                :placeholder="item.placeholder || '请选择'"
                :multiple="item.multiple || false"
                :disabled="item.disabled || false"
                :clearable="item.clearable || false"
                :multiple-limit="item.multipleLimit || 0"
                :props="item.props || {value: 'value', label: 'name'}"
                :on-ok="handleDoFun"
                @visible-change="handleVisibleChange"
            >
                <el-option
                    v-for="(o, i) in item.options || ajaxOptions"
                    :key="o[item.props ? item.props.value : 'value']"
                    :label="o[item.props ? item.props.label : 'name']"
                    :value="o[item.props ? item.props.value : 'value']"
                    :disabled="o.disabled"
                >
                    <template v-if="item.template" v-slot="scope">
                        <slot
                            :name="item.template"
                            :obj="{index: i, item}"
                        ></slot>
                    </template>
                </el-option>
            </el-select>
            <!--单选框-->
            <el-radio-group
                v-else-if="item.type === 'radio'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                :disabled="item.disabled || false"
                @change="handleChange"
            >
                <component
                    v-for="o in item.options || ajaxOptions"
                    :is="item.button ? 'el-radio-button' : 'el-radio'"
                    :key="o[item.props ? item.props.value : 'value']"
                    :label="o[item.props ? item.props.value : 'value']"
                    :disabled="o.disabled"
                    :border="item.border"
                    >{{ o[item.props ? item.props.label : 'name'] }}</component
                >
            </el-radio-group>
            <!--省市区-->
            <sb-province
                v-else-if="item.type === 'province'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                @chooseData="handleChooseData"
            ></sb-province>
            <!--复选框-->
            <el-checkbox-group
                v-else-if="item.type === 'checkbox'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                :disabled="item.disabled || false"
                @change="handleChange"
            >
                <el-checkbox
                    v-if="item.multi"
                    :indeterminate="indeterminate"
                    v-model="checkAll"
                    @change="handleCheckAllChange"
                    >全选</el-checkbox
                >
                <component
                    v-for="o in item.options || ajaxOptions"
                    :is="item.button ? 'el-checkbox-button' : 'el-checkbox'"
                    :key="o[item.props ? item.props.value : 'value']"
                    :label="o[item.props ? item.props.value : 'value']"
                    :disabled="o.disabled"
                    :checked="o.checked"
                    :border="item.border"
                    >{{ o[item.props ? item.props.label : 'name'] }}</component
                >
            </el-checkbox-group>
            <!--开关-->
            <el-switch
                v-else-if="item.type === 'switch'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                :active-value="item.activeValue || true"
                :inactive-value="inactiveFun(item.inactiveValue)"
                :disabled="item.disabled || false"
                :readonly="item.readonly || false"
            ></el-switch>
            <!--滑块-->
            <el-slider
                style="width:100%;padding:0px 10px"
                v-else-if="item.type === 'slider'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                :disabled="item.disabled || false"
                :readonly="item.readonly || false"
                :min="item.min || 0"
                :max="item.max || 100"
                :step="item.step || 1"
            ></el-slider>
            <!--链接-->
            <div :style="{'text-align':item.control.align}" v-else-if="item.type === 'link'">   
                <el-link 
                    v-if="item.control.operType=='wai'"
                    type="primary" 
                    v-bind="$attrs"
                    v-on="$listeners"
                    :underline="false"
                    :disabled="item.disabled || false"
                    :href="item.control.href" 
                    :target="item.control.target">{{ item.control.modelValue }}</el-link>
                <div v-else>
                    <el-button size="mini" type="primary" @click="pushNewPage(item)">{{ item.control.modelValue }}</el-button>
                </div>
            </div>
            <!--级联选择器 :props="{ checkStrictly: true }"父子节点取消选中关联-->
            <el-cascader
                v-else-if="item.type === 'cascader'"
                v-bind="$attrs"
                v-on="$listeners"
                :size="item.size || 'mini'"
                :disabled="item.disabled || false"
                :readonly="item.readonly || false"
                :clearable="true"
                filterable
                :options="item.options || ajaxOptions"
                :show-all-levels="item.showAllLevels || false"
                :placeholder="item.placeholder || '请选择' + item.label || '请选择'"
                @change="handleChange"
                :props="item.props || {value: 'value', label: 'label'}"
            ></el-cascader>
            <!-- 下拉树形控件 -->
            <sb-treeSelect
                v-else-if="item.type === 'treeSelect'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :data="item.data || ajaxOptions"
                :props="item.props || {value: 'value', label: 'label', children: 'children'}"
                :placeholder="item.placeholder || '请选择' + item.label || '请选择'"
                :disabled="item.disabled || false"
                :readonly="item.readonly || false"
                :clearable="item.clearable || false"
                :show-checkbox="item.multiple"
                :node-key="item.props?item.props.value:'id'"
                @chooseData="handleChooseData"
            ></sb-treeSelect>
            <!-- 下拉表格控件 -->
            <sb-downTable
                v-else-if="item.type === 'downTable'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :dialogData="item.dialogData"
                :appFormValue="formVal"
                :placeholder="item.placeholder || '请选择' + item.label || '请选择'"
                @chooseData="handleChooseData"
            ></sb-downTable>
            <!--时间选择框-->
            <div v-else-if="item.type === 'time'">
                <el-time-picker
                    v-if="item.isRange"
                    :value-format="item.valueFormat || 'HH:mm:ss'"
                    v-bind="$attrs"
                    v-on="$listeners"
                    :size="item.size || 'mini'"
                    :disabled="item.disabled || false"
                    :readonly="item.readonly || false"
                    :editable="item.editable || true"
                    @change="handleChange"
                    :placeholder="
                        item.placeholder || '请选择' + item.label || '请选择'
                    "
                    range-separator="至"
                    start-placeholde="开始时间"
                    end-placeholde="结束时间"
                    :is-range="item.isRange || false"
                ></el-time-picker>
                <el-time-select
                    v-else-if="item.isSelect"
                    v-bind="$attrs"
                    v-on="$listeners"
                    :size="item.size || 'mini'"
                    :disabled="item.disabled || false"
                    :readonly="item.readonly || false"
                    :editable="item.editable || true"
                    @change="handleChange"
                    :placeholder="
                        item.placeholder || '请选择' + item.label || '请选择'
                    "
                    :picker-options="item.pickerOptions || true"
                ></el-time-select>
                <el-time-picker
                    v-else
                    :value-format="item.valueFormat || 'HH:mm:ss'"
                    v-bind="$attrs"
                    v-on="$listeners"
                    :size="item.size || 'mini'"
                    :disabled="item.disabled || false"
                    :readonly="item.readonly || false"
                    :editable="item.editable || true"
                    @change="handleChange"
                    :placeholder="
                        item.placeholder || '请选择' + item.label || '请选择'
                    "
                    :picker-options="
                        handleTimePickerOptions(item.pickerOptions)
                    "
                ></el-time-picker>
            </div>
            <!--日期(时间)选择框-->
            <el-date-picker
                v-else-if="item.type === 'date'"
                :type="item.subtype || 'datetime'"
                :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                :format="
                    item.viewFormat || item.valueFormat || 'yyyy-MM-dd HH:mm:ss'
                "
                :size="item.size || 'mini'"
                :disabled="item.disabled || false"
                :readonly="item.readonly || false"
                :editable="item.editable || true"
                :placeholder="
                    item.placeholder || '请选择' + item.label || '请选择'
                "
                v-bind="$attrs"
                v-on="$listeners"
                range-separator="至"
                start-placeholde="开始时间"
                end-placeholde="结束时间"
                :default-time="
                    item.defaultTime ||
                        (!item.subtype ||
                        (item.subtype && item.subtype.indexOf('time') > -1)
                            ? item.subtype == 'datetimerange'
                                ? ['00:00:00', '23:59:59']
                                : '00:00:00'
                            : null)
                "
                @change="handleChange"
                :picker-options="handlePickerOptions(item.pickerOptions)"
            ></el-date-picker>
            <!--附件上传-->
            <sb-el-upload
                v-else-if="item.type === 'upload'"
                v-bind="$attrs"
                v-on="$listeners"
                :upload="item"
                @uploadData="handleUploadData"
                :on-ok="handleDoFun"
                @uploadHttpRequest="uploadHttpRequest"
            ></sb-el-upload>
            <!--sb附件上传-->
            <sb-upload
                v-else-if="item.type === 'sbUpload'"
                v-bind="$attrs"
                v-on="$listeners"
                :upload="item"
                @uploadData="handleUploadData"
                :on-ok="handleDoFun"
                @uploadHttpRequest="uploadHttpRequest"
                :disabled="item.disabled || false"
                :style="{padding: '0 10px', width: '100%', display: 'flex', 'justify-content': control.buttonAlign?control.buttonAlign:'left'}"
            ></sb-upload>
            <!--评分-->
            <el-rate
                v-else-if="item.type === 'rate'"
                v-bind="$attrs"
                :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                text-color="#ff9900"
                v-on="$listeners"
                :allow-half="item.allowHalf || false"
                :disabled="item.disabled || false"
                :show-text="item.showText || false"
                :texts="item.texts"
                :show-score="item.showScore || false"
            ></el-rate>
            <!--颜色选择器-->
            <el-color-picker
                v-else-if="item.type === 'color'"
                v-bind="$attrs"
                :color-format="item.format"
                v-on="$listeners"
                :size="item.size || 'mini'"
                :disabled="item.disabled || false"
            ></el-color-picker>
            <!--图片-->
            <el-image
                v-else-if="item.type === 'img'"
                :fit="item.fit || 'contain'"
                ><el-input></el-input
            ></el-image>
            <sb-img v-else-if="item.type === 'sbImg'" :item="item"></sb-img>
            <!--二维码-->
            <sb-qrcode
                v-else-if="item.type === 'qrcode'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
            ></sb-qrcode>
            <sb-echarts
                v-else-if="item.type === 'echarts'"
                :item="item"
                :keyVal="keyVal"
                :control="control"
            ></sb-echarts>
          <sb-map
              v-else-if="item.type === 'map'"
              :item="item"
              :keyVal="keyVal"
              :control="control"
          ></sb-map>
          <sb-show-img
              v-else-if="item.type === 'showImg'"
              :item="item"
              :keyVal="keyVal"
              :control="control"
          ></sb-show-img>
            <sb-quotepage
                v-else-if="item.type === 'quotePage'"
                :item="item"
                :keyVal="keyVal"
                :control="control"   
            ></sb-quotepage>
            <!--按键-->
          <el-button
              v-else-if="item.type === 'button'"
              :size="item.size || 'medium'"
              :disabled="item.disabled || false"
              @click="clickBtn"
          >  {{item.control.modelValue}}</el-button>
            <!--template-->
            <template
                v-else-if="item.type === 'template'"
                v-bind="$attrs"
                v-on="$listeners"
            >
                <slot :name="item.template" :obj="{item}"></slot>
            </template>

            <div
                v-else-if="item.type === 'text'"
                v-html="keyVal || ''"
                v-bind="$attrs"
                v-on="$listeners"
                :prop="item.key"
                :title="keyVal"
                :style="{width:'100%',overflow:'hidden',whiteSpace:'nowrap',textOverflow:'ellipsis',visibility:(item.key=='blank'&&!item.label?'hidden':'visible')}"
            >
                {{ keyVal || '' }}
            </div>

            <div
                v-else-if="item.type === 'tip'"
                v-bind="$attrs"
                v-on="$listeners"
                :prop="item.key"
                :style="{width:'100%',height:'40px',textAlign:item.align,backgroundColor:item.bgColor,overflow:'hidden',whiteSpace:'nowrap',textOverflow:'ellipsis',paddingLeft:'5px',lineHeight:'40px',borderLeft:'5px solid #39aef5',fontSize: '16px',color: '#333',fontWeight: '700',margin: '10px 0px'}"
            >
                {{ item.value || '' }}
            </div>

            <!--关联表单-->
            <sb-choose-data
                v-else-if="item.type === 'association'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                @chooseData="handleChooseData"
            ></sb-choose-data>

            <!--卡片布局-->
            <sb-card
                v-else-if="item.type === 'card'"
                v-bind="$attrs"
                v-on="$listeners"
                :formVal="formVal"
                :gps="gps"
                :item="item"
                @chooseCardData="handleChooseCardData"
            ></sb-card>

            <!--回显表格组件-->
            <sb-echoTable
                v-else-if="item.type === 'dialogEchoTable'"
                v-bind="$attrs"
                v-on="$listeners"
                :formVal="formVal"
                :gps="gps"
                :item="item"
                @chooseCardData="handleChooseCardData"
            ></sb-echoTable>
            <!-- 回显表格组件按钮 -->
            <sb-echoBtn
                v-else-if="item.type === 'echoBtn'"
                v-bind="$attrs"
                v-on="$listeners"
                :appFormValue="formVal"
                :item="item"
                :dialogData="item.dialogData"
                @chooseData="handleChooseData"
            ></sb-echoBtn>

            <!--折叠面板-->
            <sb-collapse
                v-else-if="item.type === 'collapse'"
                v-bind="$attrs"
                v-on="$listeners"
                :formVal="formVal"
                :gps="gps"
                :item="item"
                @chooseCardData="handleChooseCardData"
            ></sb-collapse>

            <!--标签页-->
            <sb-tabs
                v-else-if="item.type === 'tabs'"
                v-bind="$attrs"
                v-on="$listeners"
                :formVal="formVal"
                :gps="gps"
                :item="item"
                @chooseCardData="handleChooseCardData"
            ></sb-tabs>
            <org
                v-else-if="item.type === 'org'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :on-ok="handleDoFun"
                @chooseData="handleChooseData"
            ></org>
            <company
                v-else-if="item.type === 'company'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :on-ok="handleDoFun"
                @chooseData="handleChooseData"
                @uploadHttpRequest="uploadHttpRequest"
            ></company>
            <user
                v-else-if="item.type === 'user'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :on-ok="handleDoFun"
                @chooseData="handleChooseData"
            ></user>
            <peopleGroup
                v-else-if="item.type === 'peopleGroup'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :on-ok="handleDoFun"
                @chooseData="handleChooseData"
            ></peopleGroup>
            <!-- 角色组件 -->
            <role
                v-else-if="item.type === 'role'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :on-ok="handleDoFun"
                @chooseData="handleChooseData"
            ></role>
            <schedule
                v-else-if="item.type === 'schedule'"
                v-bind="$attrs"
                v-on="$listeners"
                :control="item.control"
                @chooseData="handleChooseData"
            ></schedule>
            <!-- 主子表 -->
            <sublist
                v-else-if="item.type === 'sublist'"
                v-bind="$attrs"
                v-on="$listeners"
                :gps="gps"
                :item="item"
                :appFormValue="formVal"
                :tableInfo="item.tableInfo"
                @chooseData="handleChooseData"
            ></sublist>
            <!-- 选择弹窗 -->
            <longdialogs
                v-else-if="item.type === 'dialog'"
                v-bind="$attrs"
                v-on="$listeners"
                :appFormValue="formVal"
                :item="item"
                :dialogData="item.dialogData"
                @chooseData="handleChooseData"
            ></longdialogs>
            <sb-group
                v-else-if="item.type === 'group'"
                v-bind="$attrs"
                v-on="$listeners"
                :item="item"
                :dialogData="item.dialogData"
                :appFormValue="formVal"
                :placeholder="item.placeholder || '请选择' + item.label || '请选择'"
                @chooseData="handleChooseData"
            ></sb-group>
            <!-- 内部选择弹窗 -->
            <interiorDialog
                v-else-if="item.type === 'interiorDialog'"
                v-bind="$attrs"
                v-on="$listeners"
                :appFormValue="formVal"
                :item="item"
                :dialogData="item.dialogData"
                @chooseData="handleChooseData"
                @chooseData2="handleChooseData2"
            ></interiorDialog>
            <echoTable
                v-else-if="item.type === 'echoTable'"
                v-bind="$attrs"
                v-on="$listeners"
                :appFormValue="formVal"
                :item="item"
            ></echoTable>
          <sb-html
              v-else-if="item.type === 'html'"
              v-bind="$attrs"
              v-on="$listeners"
              :appFormValue="formVal"
              :item="item"
          ></sb-html>
          <sb-alert
              v-else-if="item.type === 'alert'"
              v-bind="$attrs"
              v-on="$listeners"
              :appFormValue="formVal"
              :item="item"
          ></sb-alert>
            <!-- 头部标题 -->
            <div
                v-else-if="item.type === 'txt'"
                v-bind="$attrs"
                v-on="$listeners"
                class="orderTitle"
                :style="{'font-size': item.control.fontSize + 'px','font-weight': item.control.weight? 'bold': '',color: item.control.color,'width':'100%','text-align':'center'}" 
                v-html="item.control.modelValue"
            >
                {{ keyVal || '' }}
            </div>

            <!-- 分割线 -->
            <div
                v-else-if="item.type === 'divider'"
                style="width:100%"
            >
                <el-divider v-bind="item.control">
                    <span
                        :style="{
                            'font-size': item.control.dividerSize + 'px',
                            color: item.control.dividerColor,
                        }"
                        >{{ item.item && item.item.label }}</span
                    >
                    <span
                        v-if="item.control.subtitleText"
                        :style="{
                            'font-size':
                                item.control.subtitleSize + 'px',
                            color: item.control.subtitleColor,
                            'margin-left': '10px',
                        }"
                        >{{ item.control.subtitleText }}</span
                    >
                </el-divider>
            </div>

        
            <div v-else v-bind="$attrs" v-on="$listeners" :prop="item.key">
                {{ keyVal || '未知控件类型' }}
            </div>
            <!--<span v-else>未知控件类型</span><slot v-else v-bind="$attrs" v-on="$listeners" :prop="item.key">{{label || '未知控件类型'}}</slot>-->
        </el-form-item>
    </div>
</template>
<script>
import {getDictList,getApiList, uumsGetDictList} from '@/api/public';
import store from "@/store";
export default {
    name: 'SbElFormItem',
    props: {
        item: {
            type: Object,
            required: true,
        },
        onOk: {
            type: Function,
        },
        keyVal: {},
        formVal: {},
        control: {
            type: Object,
            default:()=>{
                return {}
            }
        },
        gps: {},
        lw: {
            type: String,
        },
        currentPageType: {
            type: String,
            // required: true,
        }
    },
    data() {
        return {
            ajaxOptions: [],
            indeterminate: true,
            checkAll: false,
            currentValue: this.value
        };
    },
    computed: {
      currentLabel() {
        let label = ''
            // generalPage: 通用类，不显示label。formPage：表单页面，显示label
        if (this.currentPageType==='formPage' && !this.item.showLabel) {
          // showLabel = false(显示) true（不显示）
            label = this.item.label
        }
        return label
      },
        classObject: function () {
            return {
                'hide': this.item && this.item.show == 'false',
            }
        }
    },
    created() {
        if (this.item.dictType && !this.item.options) this.getRequestDictList();
        if (this.item.apiUrl && !this.item.options) this.getRequestApiList();
        if (this.item.fun) {
            switch (this.item.type) {
                case 'select':
                case 'switch':
                case 'checkbox':
                case 'cascader':
                case 'radio':
                    if (
                        !this.item.options ||
                        (this.item.options && this.item.options.length === 0)
                    )
                        this.handleInputBtn();
                    break;
            }
        }
        //this.nullTextFun();新增一个为空的选项默认为请选择


    },
    methods: {
        pushNewPage: function(item) {
            // console.log('链接跳转内部', item);
            let hrefU = item.control.pageHref.split('.')[0];
            this.$router.push({ path: hrefU });
        },
        clickBtn: function(){
            this.$emit('clickBtn', this.item.control.clickFun);
        },
        getContent: function(content) {
            // this.content = content;
            this.handleChange(content);
            this.handleUploadData(content);
        },
        handleTimePickerOptions: function() {
            if (this.item.pickerOptions) {
                let opts = this.item.pickerOptions
                    .replace(new RegExp(' ', 'g'), '')
                    .split('-');
                let s = opts[0],
                    e = opts[1];
                if (s.indexOf('|') > -1) {
                    let st = s.split('|');
                    s = this.formVal[st[0]] || st[1];
                } else if (/[\S\s](\d{1,2}):(\d{1,2}):(\d{1,2})/.test(s)) {
                    s = s;
                } else {
                    s = '00:00:00';
                }
                if (e.indexOf('|') > -1) {
                    let et = e.split('|');
                    e = this.formVal[et[0]] || et[1];
                } else if (/[\S\s](\d{1,2}):(\d{1,2}):(\d{1,2})/.test(e)) {
                    e = e;
                } else {
                    e = '23:59:59';
                }
                return {
                    selectableRange: `${s + '-' + e}`,
                };
            }
            return false;
        },
        handlePickerOptions: function() {
            if (this.item.disabledDate === 'beforeDateNoToday') {
                //只能选择当天以前，不含当天
                return {
                    disabledDate(time) {
                        return time.getTime() > Date.now() - 8.64e7;
                    },
                };
            }
            if (this.item.disabledDate === 'beforeDate') {
                //只能选择当天以前，含当天
                return {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    },
                };
            }
            if (this.item.disabledDate === 'afterDateNoToday') {
                //只能选择当天以后，不含当天
                return {
                    disabledDate(time) {
                        return time.getTime() < Date.now();
                    },
                };
            }
            if (this.item.disabledDate === 'afterDate') {
                //只能选择当天以后，含当天
                return {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                };
            }
            if (this.item.disabledDate === 'afterDateBeforeNum') {
                //只能选择指定某天之后多少天以前的时间，不含最后一天
                //比如：只能选择2023-07-01 到 2023-07-08之间的时间，含2023-07-08
                //用法{class: "c4",label: "结束时间",key: "endTime",type: "date",subtype: "date",valueFormat: "yyyy-MM-dd",disabledDate: "afterDateBeforeNum",dateNum: "2023-07-01#7"},
                let ssDate, num = 0, ssDateT;
                if(this.item.dateNum.indexOf("#") > -1){
                    ssDate = this.item.dateNum.split("#")[0];
                    num = parseInt(this.item.dateNum.split("#")[1]);
                }else{
                    ssDate = this.util.getNow();
                    num = parseInt(this.item.dateNum || 0);
                }
                ssDateT = new Date(ssDate).getTime() + num*24*3600*1000;
                return {
                    disabledDate(time) {
                        return (time.getTime() < new Date(ssDate) - 8.64e7) || (time.getTime() > ssDateT);
                    }
                };
            }
            if (this.item.disabledDate === 'afterNumDate') {
                //只能选择指定某天之后多少天以后的时间，不含最后一天
                //比如：开始时间是2023-07-01，结束时间需要选择开始时间之后7天的以后，也就是只能选择2023-07-08以后的时间，不含2023-07-08
                //用法同上
                let ssDate, num = 0, ssDateT;
                if(this.item.dateNum.indexOf("#") > -1){
                    ssDate = this.item.dateNum.split("#")[0];
                    num = parseInt(this.item.dateNum.split("#")[1]);
                }else{
                    ssDate = this.util.getNow();
                    num = parseInt(this.item.dateNum || 0);
                }
                ssDateT = new Date(ssDate).getTime() + num*24*3600*1000;
                return {
                    disabledDate(time) {
                        return time.getTime() < ssDateT;
                    }
                };
            }
            if (this.item.control && this.item.control.timePickerRule){
              const isDateRange = this.control.timePickerRule === 'dateRange'
              const startTime = new Date(this.control.startTime)
              const endTime =  new Date(this.control.endTime)
              return {
                disabledDate(time){
                  if(!isDateRange) {
                    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
                  }
                  if(isDateRange) {
                    return new Date(time.getTime() + 24 * 60 * 60 * 1000) <startTime || new Date(time) > endTime;
                  }
                }
              }
            }
            return false;
        },
        dateDisabledDate(time) {
            if (this.item.disabledDate === 'beforeDateNoToday')
                return time.getTime() > Date.now() - 8.64e7;
            if (this.item.disabledDate === 'beforeDate')
                return time.getTime() > Date.now();
            if (this.item.disabledDate === 'afterDateN0Today')
                return time.getTime() < Date.now() - 8.64e7;
            if (this.item.disabledDate === 'afterDate')
                return time.getTime() < Date.now();
            return true;
        },
        handleVisibleChange(type) {
            if (this.onOk && type) {
                //val存在
                if (this.item.type === 'select' && this.item.filterMethod)
                    this.onOk(this.item, 'filterMethod', '');
            }
        },
        filterMethod(val) {
            if (this.onOk) {
                //val存在
                this.onOk(this.item, 'filterMethod', val);
            }
        },
        inactiveFun(val) {
            let reV = false;
            if (typeof val) {
                reV = val;
            }
            return reV;
        },
        nullTextFun() {
            if (
                this.item.type === 'select' &&
                this.item.options &&
                !this.item.hasNoChoose
            ) {
                if (
                    this.item.options.length > 0 &&
                    this.item.options[0][
                        this.item.props ? this.item.props.value : 'value'
                    ] !== ''
                ) {
                    let chooseD = {};
                    chooseD[this.item.props ? this.item.props.value : 'value'] =
                        '';
                    chooseD[this.item.props ? this.item.props.label : 'name'] =
                        this.item.nullText || '请选择';
                    this.item.options.unshift(chooseD);
                    // this.item.options=JSON.parse(JSON.stringify(this.item.options));
                }
            }
        },
        getRequestDictList() {
            if (this.item.from) {
                uumsGetDictList(this.item.dictType).then(res => {
                    this.dictValueType(res.data);
                });
            } else {
                getDictList(this.item.dictType, this.item.isPublic).then(
                    res => {
                        this.dictValueType(res.data);
                    }
                );
            }
        },
        getRequestApiList(){
            let request = this.item.request  || 'post'
            let paramsArr = this.item.paramsArr || []
            let paramsObj = {}
            if(paramsArr.length>0){
                paramsArr.forEach(element => {
                    if(element.type == '2'){
                        paramsObj[element.key] = store.state.user.user[element.value]
                    }else if(element.type == '3'){
                        paramsObj[element.key] = this.formVal[element.value]
                    }else{
                        paramsObj[element.key] = element.value
                    }
                });
            }
            getApiList(this.item.apiUrl,request,paramsObj).then(
                res => {
                    this.dictValueType(res.data);
                }
            );
        },
        dictValueType(data) {
            for (var i in data) {
                if (data[i].valueType && data[i].valueType === 'int')
                    data[i].value = parseInt(data[i].value);
            }
            if(this.item.type == 'select') {
                if(data.length>0 && data[0][this.item.props?this.item.props.value:'value']!==""){
                    let chooseD={};
                    chooseD[this.item.props?this.item.props.value:'value']="";
                    chooseD[this.item.props?this.item.props.label:'name']=(this.item.nullText || "请选择");
                    data.unshift(chooseD);
                }
            }
            this.ajaxOptions = data;
        },
        handleCheckAllChange(val) {
            let checkAllA = [];
            if (val) {
                for (var i in this.item.options) {
                    checkAllA.push(
                        this.item.options[i][
                            this.item.props ? this.item.props.value : 'value'
                        ]
                    );
                }
            }
            this.checkAll = val ? this.item.options : [];
            this.indeterminate = val;
            this.$emit('uploadUpdate', {key: this.item.key, list: checkAllA});
        },
        handleScoreData(obj) {
            this.$emit('uploadUpdate', {key: this.item.key, list: obj});
        },
        handleUploadData(opt) {
            this.$emit('uploadUpdate', {key: this.item.key, list: opt});
        },
        handleChooseData(opt) {
            // console.log({key: this.item.key, list: opt})
            this.$emit('uploadUpdate', {key: this.item.key, list: opt});
        },
        handleChooseData2(key,opt){
            this.$emit('uploadUpdate', {key:key, list: opt});
        },
        handleChooseCardData(val, item){
            this.$emit('uploadUpdate', {key: item.key, list: val});
        },
        handleInputBtn() {
            this.$emit('inputBtn', {key: this.item.key, fun: this.item.fun});
        },
        uploadHttpRequest(opt) {
            this.$emit('inputBtn', opt);
        },
        handleDoFun(obj, funName, data) {
            if (this.onOk) {
                if (this.item[funName]) {
                    let n = this.onOk(this.item, funName, data);
                    if (funName === 'beforeFun') return n;
                }
            } else {
                if (this.item[funName]) {
                    this.$emit(this.item[funName], {obj, data});
                }
            }
            if (funName === 'beforeFun') return true;
        },
        handleChange(vals) {
            if (this.onOk && this.item.changeFun) {
                this.onOk(this.item, 'changeFun', vals);
            } else {
                if (this.item.changeFun)
                    this.$emit('inputBtn', {
                        key: this.item.key,
                        fun: this.item.changeFun,
                        vals: vals,
                    });
            }
        },
        handleBlur(obj) {
            if (this.onOk && this.item.blurFun) {
                this.onOk(this.item, 'blurFun', obj.target.value);
            } else {
                if (this.item.blurFun)
                    this.$emit('inputBtn', {
                        key: this.item.key,
                        fun: this.item.blurFun,
                        vals: obj.target.value,
                    });
            }
        }
    },
};
</script>
<style>
.el-form-item .el-form-item__content {
    width: calc(100% - var(--lw));
}
.inputUpload .el-button--primary {
    background: #f5f7fa;
    border-color: #dcdfe6;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0 none;
    color: #909399;
    float: left;
}
.inputBtn .icon {
    margin-right: 0;
}
</style>
<style scoped>
::v-deep .el-form-item__content .el-input-number--mini {
    line-height: 31px;
}
::v-deep .el-rate {
    height: 32px;
    line-height: 2.3;
}
.el-rate {
    height: 32px;
    line-height: 2.3;
}
</style>
<template>
  <div>
    <div class="message tableForm" style="margin-top: 0px">
      <div class="orderTitle" style="font-weight: 700">问题线索及信访情况</div>
      <div class="flex j-s a-c m-title">
        <span>在办线索</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('now')" size="small"
            >添加在办线索</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="nowCuleList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="线索编号" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.clueNumber"
              placeholder="请输入线索编号"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="线索标题" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.clueTitle"
              placeholder="请输入线索标题"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="来源部门" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.sourceDepartment"
              placeholder="请输入来源部门"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="受理日期" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.acceptanceDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择受理日期"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="处置方式" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.disposalMethod"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="是否已查实" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.isVerified"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="所属形态" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.belongingForm"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="组织措施" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.organizationalMeasures"
              placeholder="请输入组织措施"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index,'now')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


       <div class="flex j-s a-c m-title">
        <span>已办线索</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('do')" size="small"
            >添加已办线索</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="doCuleList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="线索编号" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.clueNumber"
              placeholder="请输入线索编号"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="线索标题" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.clueTitle"
              placeholder="请输入线索标题"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="来源部门" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.sourceDepartment"
              placeholder="请输入来源部门"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="受理日期" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.acceptanceDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择受理日期"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="处置方式" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.disposalMethod"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="是否已查实" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.isVerified"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="所属形态" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.belongingForm"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="组织措施" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.organizationalMeasures"
              placeholder="请输入组织措施"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index,'do')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="flex j-s a-c m-title">
        <span>在办信访</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('nowletter')" size="small"
            >添加在办信访</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="nowLettersList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="信访编号" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.complaintNumber"
              placeholder="请输入信访编号"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="信访标题" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.complaintTitle"
              placeholder="请输入信访标题"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="来源部门" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.sourceDepartment"
              placeholder="请输入来源部门"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="受理日期" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.acceptanceDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择受理日期"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="处置方式" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.disposalMethod"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="是否已查实" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.isVerified"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="所属形态" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.belongingForm"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="组织措施" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.organizationalMeasures"
              placeholder="请输入组织措施"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index,'nowletter')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="flex j-s a-c m-title">
        <span>已办信访</span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd('doletter')" size="small"
            >添加已办信访</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="doLettersList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="信访编号" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.complaintNumber"
              placeholder="请输入信访编号"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="信访标题" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.complaintTitle"
              placeholder="请输入信访标题"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="来源部门" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.sourceDepartment"
              placeholder="请输入来源部门"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="受理日期" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.acceptanceDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择受理日期"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="处置方式" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.disposalMethod"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="是否已查实" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.isVerified"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="所属形态" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.belongingForm"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="组织措施" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.organizationalMeasures"
              placeholder="请输入组织措施"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index,'doletter')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      nowCuleList: [],
      doCuleList:[],
      nowLettersList:[],
      doLettersList:[],
      options:[],
      disposalMethodList:[
        {
          value: '处置',
          label: '处置',
        },
        {
          value: '不处置',
          label: '不处置',
        },
      ],
    };
  },
  methods: {
    handleAdd(type) {
      let obj = {
        sourceDepartment:'',
        acceptanceDate:'',
        disposalMethod:'',
        isVerified:'',
        belongingForm:'',
        organizationalMeasures:'',
      }
      if(type =='now' || type =='do'){
        obj.clueNumber =''
        obj.clueTitle =''

      } else {
        obj.complaintNumber =''
        obj.complaintTitle =''
      }
      if(type =='now'){
        obj.clueType = '1'
        this.nowCuleList.push(obj)
      } else if(type =='do' ){
        obj.clueType = '2'
        this.doCuleList.push(obj)
      } else if(type =='nowletter' ){
        obj.complaintType = '1'
        this.nowLettersList.push(obj)
      } else if(type =='doletter' ){
        obj.complaintType = '2'
        this.doLettersList.push(obj)
      }
    },
    handledelxueli(index,type){
      if(type =='now'){
        this.nowCuleList.splice(index, 1)
      } else if(type =='do' ){
        this.doCuleList.splice(index, 1)
      } else if(type =='nowletter' ){
        this.nowLettersList.splice(index, 1)
      } else if(type =='doletter' ){
        this.doLettersList.splice(index, 1)
      }
    }
  },
};
</script>
<style scoped>
.orderTitle {
  text-align: left;
  padding-left: 20px;
}
.m-title {
  width: 100%;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-right: 1px solid #ebebeb;
  margin-top: 6px;
  margin-bottom: 6px;
}
.m-title2 {
  width: 100%;
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
  padding: 6px 0;
}
::v-deep .el-button--text {
  color: rgba(192, 0, 0, 1);
}
::v-deep .tableCustom .el-input__prefix {
  top: 3px !important;
}
::v-deep .tableCustom .el-table__cell {
  padding: 4px 0 !important;
}
.mr5 {
  margin-right: 5px;
}
::v-deep .el-table__empty-block {
  min-height: 40px  !important;
}
::v-deep .el-table__empty-text {
  line-height: 40px !important;
}
</style>

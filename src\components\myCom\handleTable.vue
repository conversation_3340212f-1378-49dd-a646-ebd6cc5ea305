<template>
  <div>
    <div class="message tableForm" style="margin-top: 0px">
      <div class="orderTitle" style="font-weight: 700">处理、处分及问责情况</div>
      <div class="flex j-s a-c m-title">
        <span>党纪处分</span>
        <div class="flex a-c mr5" v-if="types!='read' ">
          <el-button type="primary" @click="handleAdd('now')" size="small"
            >添加党纪处分</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="nowCuleList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="处分事项" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.disciplinaryMatter"
              placeholder="请输入处分事项"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="处分生效时间" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.effectiveDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择处分生效时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>
         <el-table-column label="影响期" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.impactPeriod"
              placeholder="请输入影响期"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="是否在影响期内" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.isInImpactPeriod"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
    
        <el-table-column fixed="right" label="操作" width="100" v-if="types!='read' ">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index,'now')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>


       <div class="flex j-s a-c m-title">
        <span>公司处分</span>
        <div class="flex a-c mr5" v-if="types!='read' ">
          <el-button type="primary" @click="handleAdd('do')" size="small"
            >添加公司处分</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="doCuleList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
          <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="处分事项" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.disciplinaryMatter"
              placeholder="请输入处分事项"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="处分生效时间" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.effectiveDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择处分生效时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>
         <el-table-column label="影响期" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.impactPeriod"
              placeholder="请输入影响期"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="是否在影响期内" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.isInImpactPeriod"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100" v-if="types!='read' ">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index,'do')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="flex j-s a-c m-title">
        <span>党内问责</span>
        <div class="flex a-c mr5" v-if="types!='read' ">
          <el-button type="primary" @click="handleAdd('ze')" size="small"
            >添加党内问责</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="nowLettersList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
          <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column label="处分事项" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.disciplinaryMatter"
              placeholder="请输入处分事项"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="处分生效时间" align="center">
          <template v-slot:default="scope">
            <el-date-picker
              v-model="scope.row.effectiveDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              size="mini"
              placeholder="请选择处分生效时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>
         <el-table-column label="影响期" align="center">
          <template v-slot:default="scope">
            <el-input
              size="mini"
              v-model="scope.row.impactPeriod"
              placeholder="请输入影响期"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="是否在影响期内" align="center">
          <template v-slot:default="scope">
            <el-select
              v-model="scope.row.isInImpactPeriod"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100" v-if="types!='read' ">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index,'ze')"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      
    </div>
  </div>
</template>
<script>
import { getDictList } from "@/api/public";

export default {
  props: {
    list: {
      type: Object,
      default: () => {},
    },
     types: { type: String },
    
  },
  data() {
    return {
      nowCuleList: [],
      doCuleList:[],
      nowLettersList:[],
      options: [
        {
          value: "是",
          label: "是",
        },
        {
          value: "否",
          label: "否",
        },
       
      ],
    };
  },
  watch: {
    list: {
      handler(newVal, oldVal) {
        if (newVal.usDisciplinarySituationList) {
          this.nowCuleList = newVal.usDisciplinarySituationList.filter(item=>item.disciplinaryType=='1');
          this.doCuleList = newVal.usDisciplinarySituationList.filter(item=>item.disciplinaryType=='2');
          this.nowLettersList = newVal.usDisciplinarySituationList.filter(item=>item.disciplinaryType=='3');
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted(){
    
  },
  methods: {
    handleAdd(type) {
      let obj={
        disciplinaryMatter: '',
        effectiveDate: '',
        impactPeriod: '',
        isInImpactPeriod: '',
      }
      if(type == 'now'){
        obj.disciplinaryType = '1'
        this.nowCuleList.push(obj);
      }else if(type == 'do'){
        obj.disciplinaryType = '2'
        this.doCuleList.push(obj);
      }else if(type == 'ze'){
        obj.disciplinaryType = '3'
        this.nowLettersList.push(obj);
      }
    },
    handledelxueli(index,type){
      if(type == 'now'){
        this.nowCuleList.splice(index, 1)
      }else if(type == 'do'){
        this.doCuleList.splice(index, 1)
      }else if(type == 'ze'){
        this.nowLettersList.splice(index, 1)
      }
    }
  },
};
</script>
<style scoped>
.orderTitle {
  text-align: left;
  padding-left: 20px;
}
.m-title {
  width: 100%;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-right: 1px solid #ebebeb;
  margin-top: 6px;
  margin-bottom: 6px;
}
.m-title2 {
  width: 100%;
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
  padding: 6px 0;
}
::v-deep .el-button--text {
  color: rgba(192, 0, 0, 1);
}
::v-deep .tableCustom .el-input__prefix {
  top: 3px !important;
}
::v-deep .tableCustom .el-table__cell {
  padding: 4px 0 !important;
}
.mr5 {
  margin-right: 5px;
}
::v-deep .el-table__empty-block {
  min-height: 40px !important;
}
::v-deep .el-table__empty-text {
  line-height: 40px !important;
}
</style>

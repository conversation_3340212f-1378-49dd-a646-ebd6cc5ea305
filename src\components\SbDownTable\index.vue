<template>
  <el-select v-model="value" v-bind="$attrs" v-on="$listeners" :size="item.size || 'small'" :placeholder="item.placeholder || item.label || '请输入'" :disabled="item.disabled || false" :readonly="item.readonly || false" :autosize="item.autosize || false" style="width:100%;" @visible-change="visibleChange">
    <template #empty>
      <div style="padding:10px">
        <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun"></sb-el-table>
        <div style="display: flex;
    justify-content: flex-end;
    padding: 10px 0px 10px;
    margin-top: 75px;">
          <el-button type="primary" @click="handleSubmitGroup">确 定</el-button>
        </div>
      </div>
    </template>
  </el-select>
</template>
<script>
import { getDataBySuperApi, getDataBySuperApiNoPage, getDataByApiPage } from '@/api/public'
import {
  getTableList,
  getTableListNoPage
} from '@/api/public'
import util from "@/assets/js/public";
import request from "@/assets/js/request";
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    dialogData: {
      type: Object,
      required: true
    },
    appFormValue: {
      type: Object,
      required: true
    },
    value: {
      type: undefined,
      default: null,
    },
  },
  data() {
    return {
      values: "",
      table: {
        modulName: "dialogs-选择数据", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: true, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            { label: "关键词", key: "appName", type: "input" },
            // {label: "流程类型",key: "pmInstType",type: "select",dictType: "processType"}
          ],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.getTableInfo()
  },
  methods: {
    getTableInfo() {
      if (this.dialogData.options.length > 0) {
        const options = this.dialogData.options.map(element => {
          let option = {
            id: element.value,
            label: element.name,
            prop: element.value,
          };
          if(element.labelWidth) { option.width = element.labelWidth; }
          if(element.paramType) { option.align = element.paramType }
          return option;
        });
        this.table.tr = options
      } else {
        this.table.tr = []
      }

      if (this.dialogData.paramsArr&&this.dialogData.paramsArr.length > 0) {
        const formItemList = this.dialogData.paramsArr.map(element => {
          let searchInfo = {
            type: element.type?element.type:"input",
            label: element.name,
            prop: element.value,
            key: element.value
          };
          if(searchInfo.type=='1') { searchInfo.type = 'input'; }
          if(element.labelWidth) { searchInfo.labelWidth = element.labelWidth+'px'; }
          if (element.type == 'select') {
            if(element.selectype == 'async') {
              let params = {}
              if(element.protParamName){
                params[element.protParamName] = element.protParam
              }
              searchInfo.apiUrl = element.sourceFun,
              searchInfo.request = element.request,
              searchInfo.paramsObj = params
            } else if(element.selectype == 'fixed') {
              searchInfo.options = element.options
            } else if (element.selectype == 'dictType'){ 
              searchInfo.dictType = element.dictType
              searchInfo.options = ''
            }
          }
          if (['radio', 'multiple'].includes(element.type)) {
            searchInfo.type = 'select'
            if (element.type === 'multiple') {
              searchInfo.multiple = true
            }
            if(element.selectype == 'async'){
              let params = {}
              if(element.protParamName){
                params[element.protParamName] = element.protParam
              }
              searchInfo.apiUrl = element.sourceFun,
              searchInfo.request = element.request,
              searchInfo.paramsObj = params
            } else if (element.selectype == 'dictType'){ 
              formInfo.dictType = element.dictType
              formInfo.options = ''
            } else {
              searchInfo.options = element.options
            }
          }
          if (element.type == 'date') {
            searchInfo.valueFormat = element.valueFormat
            if(element.valueFormat == 'yyyy-MM' || element.valueFormat == 'yyyyMM'){
              searchInfo.subtype = 'month'
            }
            if(element.valueFormat == 'yyyy'){
              searchInfo.subtype = 'year'
            }
          }
          if(element.type == 'date2'){
            searchInfo.type = 'date'
            searchInfo.subtype = 'datetimerange'
            searchInfo.valueFormat = element.valueFormat
            searchInfo.datetimerange = element.valueFormat
          } 
          if (element.required && element.defaultValue) {
            this.table.listQuery[element.value] = element.defaultValue
          }
          if(element.paramsType) {
            if(element.paramsType=='1') {
              this.table.listQuery[element.value] = element.paramsValue;
            } else if(element.paramsType=='2') {
              this.table.listQuery[element.value] = this.$store.getters.user[element.paramsValue]
            } else {
              this.table.listQuery[element.value] = this.appFormValue[element.paramsValue]
            }
          }
          if (element.hiddenParams) {
            // if(!element.paramsType){
            //   this.table.listQuery[element.value] = element.paramsValue
            // }
          }else{
            return searchInfo;
          }
        });
        this.table.queryForm.formItemList = formItemList
      } else {
        this.table.queryForm.formItemList = []
      }
    },
    handleSubmitGroup() {
      this.$emit("chooseData", this.table.multipleSelection);
    },
    visibleChange() {
      this.getList();
    },
    // 查询列表
    getList() {
      if (this.dialogData.dialogType == '1') {
        this.table.listQuery.url = this.dialogData.dialogData.url
        if (this.dialogData.isPage) {
          this.table.loading = true;
          getDataBySuperApi(this.table.listQuery).then((res) => {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.data.content;
            this.table.total = JSON.stringify(res.data) == '{}' ? null : res.data.data.totalElements
          }).catch((err) => {
            this.table.loading = false;
          });
        } else {
          this.table.loading = true;
          getDataBySuperApiNoPage(this.table.listQuery).then((res) => {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.data;
          }).catch((err) => {
            this.table.loading = false;
          });
        }
      } else if (this.dialogData.dialogType == '2') {
        if (Object.keys(this.dialogData.api).length > 0) {
          this.getInfo()
          // .then((res) => {
          //   if (res.status === 200) {
          //     this.table.loading = false;
          //     this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.content;
          //     this.table.total = JSON.stringify(res.data) == '{}' ? null : res.data.totalElements
          //   }
          // })
        }
      } else {
        let database = this.util.toHump(this.dialogData.database);
        let keyArr = Object.keys(this.table.listQuery);
        let paramsList = {};
        keyArr.forEach(key => {
          if(key!='page'&&key!='size') {
            this.dialogData.paramsArr.forEach((item) => {
              if(item.value==key) {
                let val = this.table.listQuery[key];
                paramsList[key] = {
                  paramValue: val,
                  paramType: item.paramType
                }
              }
            })
          }
        })
        paramsList.page = this.table.listQuery.page;
        paramsList.size = this.table.listQuery.size;
        if(this.dialogData.isPage) {
          getTableList(
            process.env.VUE_APP_APPCODE,
            database,
            paramsList
          ).then((res) => {
            this.table.loading = false;
            this.table.data = res.data.content
            this.table.total = res.data.totalElements
          }).catch((err) => {
            this.table.loading = false;
          });
        } else {
          delete paramsList.page;
          delete paramsList.size;
          getTableListNoPage(
            process.env.VUE_APP_APPCODE,
            database,
            paramsList
          ).then((res) => {
            this.table.loading = false;
            this.table.data = res.data;
            // this.table.total = res.data.totalElements
          }).catch((err) => {
            this.table.loading = false;
          });
        }
      }
    },
    handelAPI() {
      const { type, reqUrl } = this.dialogData.api;
      let params = {};
      if(this.dialogData.api.params.length>0) {
        this.dialogData.api.params.forEach((item) => {
          if(item.value) {
            params[item.key] = item.value;
          }
        });
      }
      let data = {};
      if (this.dialogData.api.radio === "form-data") {
        if (this.dialogData.api.body.listValue && this.dialogData.api.body.listValue.length > 0) {
          const listValue = JSON.parse(JSON.stringify(this.dialogData.api.body.listValue));
          listValue.forEach((item) => {
            if (item.key && item.value) {
              data[item.key] = item.value
            }
          });
        }
      } else {
        if (this.dialogData.api.body.jsonValue) {
          const list = JSON.parse(JSON.stringify(this.dialogData.api.body.jsonValue));
          list.forEach((item) => {
            if (item.key && item.value) {
              data[item.key] = item.value
            }
          });
        }
      }
      return {
        type,
        reqUrl,
        params,
        data
      }
    },
    getInfo() {
      const { type, reqUrl, params, data } = this.handelAPI()
      // Object.assign(params, this.table.listQuery)
      if(this.dialogData.isPage) {
        let params1 = {
          url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}?page=${this.table.listQuery.page}&size=${this.table.listQuery.size}`),
          params: Object.assign(this.dialogData.api.param=='params'?params:data, this.table.listQuery)
        }
        getDataByApiPage(params1).then(res => {
          if (res.status === 200) {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data.content;
            this.table.total = JSON.stringify(res.data) == '{}' ? null : res.data.totalElements
          }
        })
      } else {
        delete this.table.listQuery.page;
        delete this.table.listQuery.size;
        let params1 = {
          url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}`),
          params: Object.assign(this.dialogData.api.param=='params'?params:data, this.table.listQuery)
        }
        getDataByApiPage(params1).then(res => {
          if (res.status === 200) {
            this.table.loading = false;
            this.table.data = JSON.stringify(res.data) == '{}' ? [] : res.data;
          }
        })
      }
      // return request({
      //   method: type,
      //   url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}`),
      //   params: params,
      //   data: data,
      //   contentType: "application/json;charset=UTF-8",
      // });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
.selecTree {
  max-height: 50vh;
  overflow: auto;
  padding: 5px;
}
.el-select {
  width: 100%;
}
.slotSpan {
  font-size: 14px;
}
.slotSpan b {
  font-weight: normal;
  font-size: 12px;
  color: #999;
}
.selecTree ::v-deep .el-tree-node__content {
  font-size: 14px;
}
</style>
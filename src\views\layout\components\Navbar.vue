<template>
  <el-menu class="navbar clearfix" mode="horizontal" ref="navbarRef">
    <!-- 顶部菜单 -->
    <div v-if="showsDirection=='top'" class="menuL1" :style="{width: menuL1li + 'px'}">
      <a v-if="lrIcon" class="menuL1IconL" @click="handleScroll(true)"><i class="el-icon-arrow-left"></i></a>
      <scroll-bar-transform :type="'left'" :width="menuS" ref="menuL1SB">
        <div :style="{width: menuL + 'px'}">
          <el-menu ref="topmenu" mode="horizontal" class="" :default-active="$route.path" :unique-opened="true">
            <sidebar-item :menus="menus"></sidebar-item>
          </el-menu>
        </div>
      </scroll-bar-transform>
      <a v-if="lrIcon" class="menuL1IconR" @click="handleScroll(false)"><i class="el-icon-arrow-right"></i></a>
    </div>
    <div class="avatar-container" ref="avatarRef">
      <!-- <Settings></Settings>
      <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->
      <span class="mr10 f14">您好:</span>
      <span class="mr10 f14">{{user.truename || ""}}</span>
      <span class="mr10 f14">{{'('+(user.belongOrgName || "")+')'}}</span>
      <span class="mr10 f14">{{calendar || ""}}</span>
      <span @click="logout" title="退出登录"><svg-icon icon-class="tuichudenglu" class-name="logout"></svg-icon></span>
    </div>
  </el-menu>
</template>
<script>
import Cookies from "js-cookie";
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import SidebarItem from "./Sidebar/SidebarItem";
import scrollBarTransform from "@/components/ScrollBarTransform";
import util from "@/assets/js/public";
// import { updatePwd, download } from "@/api/home";
import Screenfull from '@/components/Screenfull'
import Settings from '@/components/Settings'
export default {
  components: {
    Breadcrumb,
    Hamburger,
    scrollBarTransform,
    SidebarItem,
    Screenfull,
    Settings
  },
  data() {
    var validateoldPass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入旧密码"));
      } else {
        // if (this.ruleForm.oldpass !== '') {

        // }
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.pass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      lrIcon: false,
      showsDirection:process.env.VUE_APP_Sidebar,
      menuL1li: 0,
      menuS: 0,
      menuL: 0,
      ruleForm: {
        oldpass: "",
        pass: "",
        checkPass: "",
      },
      rules: {
        oldpass: [{ validator: validateoldPass, trigger: "blur" }],
        pass: [{ validator: validatePass, trigger: "blur" }],
        checkPass: [{ validator: validatePass2, trigger: "blur" }],
      },
      calendar: "",
      pwdDialog: false,
      authBlocs: this.$store.getters.user.authBlocs,
      authCorps: this.$store.getters.user.authCorps,
      username: "(" + this.$store.getters.user.username + ")",
      truename: this.$store.getters.user.truename,
      nickname: this.$store.getters.user.nickname,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      corpsList: [],
      chooseCorps: false,
      hasChoose: false,
    };
  },
  computed: {
    ...mapGetters(["sidebar", "user", "menus"]),
    isCollapse() {
      return true; //!this.sidebar.opened
    },
  },
  created() {
    this.calendar = this.util.getNow("yyyy年MM月dd日 星期weekday", true);
    // this.defaultKey();
  },
  mounted() {
    this.SBWidth();
    window.onresize = () => {
      return (() => {
        this.SBWidth();
      })();
    };
  },
  methods: {
    SBWidth() {
      let navzc = this.$refs.navbarRef.$el.offsetWidth; //bgn56
      let avatarRef = this.$refs.avatarRef.offsetWidth;
      this.menuL1li = navzc - avatarRef - 56 - 10 - 60 * 2 - 90;
      this.menuS = this.menuL1li - 124 * 2;
      let zc = 0;
      for (let i in this.menus) {
        let num = this.menus[i].meta.title.length;
        let len = 15 * num + 32;
        zc += len;
      }
      this.menuL = zc;
      this.lrIcon = this.menuS < this.menuL;
    },
    handleScroll(type) {
      let sb = this.$refs.menuL1SB;
      let sc = sb.$refs.scrollContainer.clientWidth;
      if (sb.left < 0 && type) {
        if (sb.left + sc > 0) sb.left = 0;
        else sb.left += sc;
      }
      let rc = sc - this.menuL;
      if (sb.left > rc && !type) {
        if (sb.left - sc < rc) sb.left = rc;
        else sb.left -= sc;
      }
    },
    toggleSideBar() {
      this.$store.dispatch("ToggleSideBar");
    },
    logout() {
      this.$confirm("确认退出登录？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          this.$store.dispatch("FedLogOut").then(() => {
            this.$router.push({ path: "/login" });
          });
        });
      })
        .catch(() => { });
    },
    changePwd() {
      this.pwdDialog = true;
      if (this.$refs["ruleForm"]) {
        this.$refs["ruleForm"].resetFields();
      }
    },
    // 修改密码
    // handleChangePwd(formName) {
    //   this.$refs[formName].validate((valid) => {
    //     if (valid) {
    //       updatePwd({
    //         employeeId: this.$store.getters.user.id,
    //         oldPwd: util.encrypt(this.ruleForm.oldpass),
    //         newPwd: util.encrypt(this.ruleForm.pass),
    //       }).then((res) => {
    //         this.pwdDialog = false;
    //         setTimeout(this.logout, 2000);
    //       });
    //     } else {
    //       return false;
    //     }
    //   });
    // },
    help() {
      // download()
      //   .then((res) => {
      //       console.log(res);
      //     this.util.blobDownload(
      //       res.data,
      //       this.util.replaceXlsxExtension(res.filename)
      //     );
      //   })
      //   .catch((err) => {
      //     console.log("catch", err);
      //   });
      // window.open('http://************:8088/iarchiveadmin/assets/电子档案管理系统操作手册.doc');
    },
  },
};
</script>
<style scoped>
.menuL1 {
  height: 54px;
  overflow: hidden;
  position: relative;
  z-index: 8;
  float: left;
  width: auto;
  padding: 0 24px;
}
.menuL1li li {
  width: auto;
  float: left;
  line-height: 54px;
  font-size: 14px;
  color: #fff;
  padding: 0 10px;
}
.menuL1li li.li_active,
.menuL1li li:hover {
  background: #688eff;
  cursor: pointer;
}
.menuL1li li .icon {
  margin: 0 10px 0 0;
  color: #fff;
}
.menuL1li li .xia {
  font-size: 8px;
  margin: 0 0 0 10px;
}
.menuL1IconL,
.menuL1IconR {
  position: absolute;
  line-height: 54px;
  color: #fff;
  top: 0;
  padding: 0;
  background: transparent;
}
/* .menuL1IconL:hover,.menuL1IconR:hover{background:#688EFF;} */
.menuL1IconL {
  left: 0;
}
.menuL1IconR {
  right: 0;
}
.el-menu {
  background-color: transparent;
  height: 54px;
}
.el-menu.el-menu--horizontal {
  border: 0 none;
}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item {
  color: #515151;
}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-submenu__title,
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item {
  background-color: transparent;
  height: 54px;
  line-height: 54px;
  color: #fff;
  /* padding: 0 35px 0 15px; */
  padding: 0 16px;
  font-size: 15px;
  font-weight: 400;
}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item {
  padding: 0 15px;
}
.menuL1 ::v-deep  .icon {
  vertical-align: -0.45em !important;
  margin-right: 6px !important;
}
.menuL1 ::v-deep  .menuL1IconL .icon,
.menuL1 ::v-deep  .menuL1IconR .icon {
  margin: 0 !important;
}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-submenu__title i {
  color: #fff;
}
.menuL1 ::v-deep  .el-submenu__icon-arrow {
  right: 15px;
  margin-top: -4px;
  display: none;
}
.menuL1 ::v-deep  .el-submenu,
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item {
  width: auto;
  float: left;
  line-height: 54px;
}
.menuL1 .menu-wrapper {
 display: flex;
}
.menuL1 .menu-wrapper ::v-deep  .el-submenu.is-active .el-submenu__title {
  border-left: 0px solid #507af6 !important;
  background: #8dc21f !important;
  color: #fff !important;
}
.chooseZoneT {
  padding-left: 15px;
}
.chooseZoneT .icon {
  margin-left: 4px;
  font-size: 10px;
  margin-right: 6px;
}
.choose_tit {
  font-size: 14px;
  font-weight: bold;
  border-left: 3px solid #409eff;
  line-height: 32px;
  border-bottom: 1px solid #ddd;
  color: #333;
  padding: 0 0 0 8px;
}
.choose_nr {
  padding: 0px 0px 15px;
}
.choose_a {
  border: 1px solid #e9e9eb;
  border-radius: 4px;
  margin: 15px 20px 0 0;
  padding: 6px 18px;
  position: relative;
  display: inline-block;
  color: #909399;
  background: #f4f4f5;
}
.choose_a .icon {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 10px;
  color: #409eff;
  margin-right: 0;
}
.choose_a_hover {
  border-color: #d9ecff;
  color: #409eff;
  background: #ecf5ff;
}
.choose_a_hover .icon {
  color: #409eff;
}
.navbar {
  position: fixed;
  top: 0;
  left: 380px;
  right: 0;
  z-index: 8;
  height: 54px;
  line-height: 54px;
  border-radius: 0px;
  background: #c00000;
  color: #fff;
}
.navbar .avatar-container {
  float: right;
  height: 54px;
  margin-left: 50px;
  display: flex;
}
.navbar .avatar-container .avatar-wrapper {
  cursor: pointer;
  position: relative;
}
.navbar .avatar-container .avatar-wrapper .user-avatar {
  width: 36px;
  height: 36px;
  margin-right: 5px;
  margin-left: 10px;
  background: #fff;
  border-radius: 50%;
}
.navbar .avatar-container .avatar-wrapper .el-icon-caret-bottom {
  position: absolute;
  right: -20px;
  top: 25px;
  font-size: 12px;
}
.avatar-wrapper .icon {
  font-size: 10px;
}
.el-dropdown {
  color: #fff;
}
.tit {
  display: inline-block;
  width: 1px;
  height: 30px;
  background: #e9e9e9;
  position: relative;
  top: 5px;
  left: 7px;
}
.logout{
  font-size: 14px;
  margin-left: 20px;
}
.icon {
  width: 1.5em;
  height: 1.5em;
  vertical-align: -0.3em;
  fill: currentColor;
  overflow: hidden;
  cursor: pointer;
}
</style>

<template>
  <div>
    <div class="message tableForm" style="margin-top: 0px">
      <div class="orderTitle" style="font-weight: 700">
        廉政回复信息
      </div>
      <div class="flex j-s a-c m-title2">
        <span></span>
        <div class="flex a-c mr5">
          <el-button type="primary" @click="handleAdd()" size="small"
            >添加记录</el-button
          >
        </div>
      </div>
      <el-table
        class="tableCustom"
        :data="nowList"
        style="width: 100%"
        border
        :cell-style="{ background: '#ffffff' }"
      >
        <el-table-column
        type="index"
        label="序号"
        width="50">
        </el-table-column>
        <el-table-column label="来函号" align="center">
          <template v-slot:default="scope">
             <el-input
             size="mini"
              v-model="scope.row.incomingLetterNumber"
              placeholder="请输入来函号"
            ></el-input>
          </template>
        </el-table-column>
          <el-table-column label="回复单位" align="center">
          <template v-slot:default="scope">
             <el-input
             size="mini"
              v-model="scope.row.replyUnit"
              placeholder="请输入回复单位"
            ></el-input>
          </template>
        </el-table-column>
         <el-table-column label="回复函号" align="center">
          <template v-slot:default="scope">
             <el-input
             size="mini"
              v-model="scope.row.replyLetterNumber"
              placeholder="请输入回复函号"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="回复时间" align="center">
          <template v-slot:default="scope">
            <el-date-picker
            v-model="scope.row.replyTime"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            size="mini"
            placeholder="请选择回复时间"
          >
          </el-date-picker>
          </template>
        </el-table-column>
      
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="handledelxueli(scope.$index)"
              type="text"
              size="small"
              >【删除】</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getDictList } from "@/api/public";

export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    gps: {
      type: Object,
      default() {
        return this.$route.query;
      },
     }
  },
  data() {
    return {
      nowList:[],
        options: [
            {
                value: '1',
                label: '1',
            },
            {
                value: '2',
                label: '2',
            },
            {
                value: '3',
                label: '3',
            },
        ]
    }
  },
  methods: {
    handleAdd() {
      this.nowList.push({
        incomingLetterNumber: '',
        replyUnit: '',
        replyLetterNumber: '',
        replyTime: '',
      })
    },
    handledelxueli(index){
      this.nowList.splice(index, 1);
    }
  }
}

</script>
<style scoped>
.orderTitle{
    text-align: left;
      padding-left: 20px;
}
.m-title2 {
  width: 100%;
  padding: 0px 5px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
  padding: 6px 0;
}
::v-deep .el-button--text{
    color: rgba(192, 0, 0, 1);
}
::v-deep .tableCustom .el-input__prefix {
    top: 3px !important;
}
 ::v-deep .tableCustom .el-table__cell{
    padding: 4px 0 !important;
 }
.mr5 {
  margin-right: 5px;
}
::v-deep .el-table__empty-block {
  min-height: 40px;
}
::v-deep .el-table__empty-text {
  line-height: 40px;
}
</style>
